import torch
import torch.nn as nn
import math

# 参数设置 (与原模型保持一致)
feature_size = 128
smiles_kernel_num = 32
smiles_kernel_size = 8
smiles_input_size = 128
atom_dic_length = 65

class DrugSMILESCoding(nn.Module):
    """SMILES序列编码模块 - 与原模型完全一致"""
    def __init__(self, hid_dim=smiles_input_size, out_dim=feature_size, vocab_size=atom_dic_length,
                 channel=smiles_kernel_num, kernel_size=smiles_kernel_size, dropout_rate=0.3):
        super(DrugSMILESCoding, self).__init__()
        self.embedding = nn.Embedding(vocab_size, embedding_dim=hid_dim)
        self.embedding_dropout = nn.Dropout(dropout_rate * 0.5)
        
        # 卷积层 + BatchNorm + Dropout
        self.conv1 = nn.Conv1d(hid_dim, channel, kernel_size, padding=kernel_size-1)
        self.bn1 = nn.BatchNorm1d(channel)
        self.dropout1 = nn.Dropout(dropout_rate * 0.6)
        
        self.conv2 = nn.Conv1d(channel, channel*2, kernel_size, padding=kernel_size-1)
        self.bn2 = nn.BatchNorm1d(channel*2)
        self.dropout2 = nn.Dropout(dropout_rate * 0.7)
        
        self.conv3 = nn.Conv1d(channel*2, channel*4, kernel_size, padding=kernel_size-1)
        self.bn3 = nn.BatchNorm1d(channel*4)
        self.dropout3 = nn.Dropout(dropout_rate)
        
        self.act = nn.LeakyReLU(0.2)
        self.globalmaxpool = nn.AdaptiveMaxPool1d(1)
        
        # 全连接层正则化
        self.fc1 = nn.Linear(channel*4, out_dim)
        self.fc_dropout = nn.Dropout(dropout_rate)

    def forward(self, x):
        # 嵌入 + 轻度dropout
        x = self.embedding(x)
        x = self.embedding_dropout(x)
        x = x.permute(0, 2, 1)
        
        # 第一层卷积 + BN + Dropout
        x = self.conv1(x)
        x = self.bn1(x)
        x = self.act(x)
        x = self.dropout1(x)
        
        # 第二层卷积 + BN + Dropout
        x = self.conv2(x)
        x = self.bn2(x)
        x = self.act(x)
        x = self.dropout2(x)
        
        # 第三层卷积 + BN + Dropout
        x = self.conv3(x)
        x = self.bn3(x)
        x = self.act(x)
        x = self.dropout3(x)
        
        # 全局池化 + 全连接
        x = self.globalmaxpool(x)
        x = x.squeeze(-1)
        x = self.fc_dropout(x)
        x = self.fc1(x)
        return x


class FishNOECPredictorSMILESOnly(nn.Module):
    """FishLC50预测模型 - 仅SMILES通道 (消融实验版本)"""
    def __init__(self, dropout_rate=0.4):
        super(FishNOECPredictorSMILESOnly, self).__init__()
        # 只使用SMILES编码器
        self.smiles_encoder = DrugSMILESCoding(dropout_rate=dropout_rate * 0.75)
        
        # 特征处理层 (输入维度调整为单通道)
        self.feature_bn = nn.BatchNorm1d(feature_size)  # 只有SMILES特征
        self.feature_dropout = nn.Dropout(dropout_rate * 0.5)
        
        # 预测层 + 渐进式dropout (输入维度调整)
        self.fc1 = nn.Linear(feature_size, 512)  # 输入维度从feature_size*2改为feature_size
        self.fc1_bn = nn.BatchNorm1d(512)
        self.fc1_dropout = nn.Dropout(dropout_rate * 0.6)
        
        self.fc2 = nn.Linear(512, 256)
        self.fc2_bn = nn.BatchNorm1d(256)
        self.fc2_dropout = nn.Dropout(dropout_rate * 0.8)
        
        self.fc3 = nn.Linear(256, 128)
        self.fc3_bn = nn.BatchNorm1d(128)
        self.fc3_dropout = nn.Dropout(dropout_rate)
        
        self.fc4 = nn.Linear(128, 2)  # 最终输出层
        
        self.act = nn.LeakyReLU(0.2)
        
        # 权重初始化
        self._init_weights()

    def _init_weights(self):
        """Xavier初始化权重"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Conv1d):
                nn.init.kaiming_uniform_(m.weight, mode='fan_in', nonlinearity='leaky_relu')
            elif isinstance(m, nn.BatchNorm1d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)

    def forward(self, smiles_data, point_data=None):
        """
        前向传播 - 只使用SMILES数据
        Args:
            smiles_data: SMILES序列数据
            point_data: 3D点云数据 (消融实验中忽略)
        """
        # 只使用SMILES通道特征提取
        smiles_features = self.smiles_encoder(smiles_data)
        
        # 特征处理 (无需融合，直接处理SMILES特征)
        x = self.feature_bn(smiles_features)
        x = self.feature_dropout(x)
        
        # 第一层预测
        x = self.fc1(x)
        x = self.fc1_bn(x)
        x = self.act(x)
        x = self.fc1_dropout(x)
        
        # 第二层预测
        x = self.fc2(x)
        x = self.fc2_bn(x)
        x = self.act(x)
        x = self.fc2_dropout(x)
        
        # 第三层预测
        x = self.fc3(x)
        x = self.fc3_bn(x)
        x = self.act(x)
        x = self.fc3_dropout(x)
        
        # 最终输出层
        x = self.fc4(x)
        return x


class FishLC50PredictorSMILESOnly(nn.Module):
    """FishLC50预测模型 - 仅3D点云通道 (消融实验版本)"""
    def __init__(self, dropout_rate=0.4):
        super(FishLC50PredictorSMILESOnly, self).__init__()
        # 导入3D点云编码器 (需要从原模型复制)
        from model import DrugPointCoding
        
        # 只使用3D点云编码器
        self.point_encoder = DrugPointCoding(dropout_rate=dropout_rate * 0.75)
        
        # 特征处理层 (输入维度调整为单通道)
        self.feature_bn = nn.BatchNorm1d(feature_size)  # 只有点云特征
        self.feature_dropout = nn.Dropout(dropout_rate * 0.5)
        
        # 预测层 + 渐进式dropout (输入维度调整)
        self.fc1 = nn.Linear(feature_size, 512)  # 输入维度从feature_size*2改为feature_size
        self.fc1_bn = nn.BatchNorm1d(512)
        self.fc1_dropout = nn.Dropout(dropout_rate * 0.6)
        
        self.fc2 = nn.Linear(512, 256)
        self.fc2_bn = nn.BatchNorm1d(256)
        self.fc2_dropout = nn.Dropout(dropout_rate * 0.8)
        
        self.fc3 = nn.Linear(256, 128)
        self.fc3_bn = nn.BatchNorm1d(128)
        self.fc3_dropout = nn.Dropout(dropout_rate)
        
        self.fc4 = nn.Linear(128, 2)  # 最终输出层
        
        self.act = nn.LeakyReLU(0.2)
        
        # 权重初始化
        self._init_weights()

    def _init_weights(self):
        """Xavier初始化权重"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Conv1d):
                nn.init.kaiming_uniform_(m.weight, mode='fan_in', nonlinearity='leaky_relu')
            elif isinstance(m, nn.BatchNorm1d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)

    def forward(self, smiles_data=None, point_data=None):
        """
        前向传播 - 只使用3D点云数据
        Args:
            smiles_data: SMILES序列数据 (消融实验中忽略)
            point_data: 3D点云数据
        """
        # 只使用3D点云通道特征提取
        point_features = self.point_encoder(point_data)
        
        # 特征处理 (无需融合，直接处理点云特征)
        x = self.feature_bn(point_features)
        x = self.feature_dropout(x)
        
        # 第一层预测
        x = self.fc1(x)
        x = self.fc1_bn(x)
        x = self.act(x)
        x = self.fc1_dropout(x)
        
        # 第二层预测
        x = self.fc2(x)
        x = self.fc2_bn(x)
        x = self.act(x)
        x = self.fc2_dropout(x)
        
        # 第三层预测
        x = self.fc3(x)
        x = self.fc3_bn(x)
        x = self.act(x)
        x = self.fc3_dropout(x)
        
        # 最终输出层
        x = self.fc4(x)
        return x