import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import torch
import torch.nn as nn
from torch.utils.data import TensorDataset, DataLoader
from sklearn.model_selection import train_test_split
from sklearn.metrics import roc_curve, auc

from model import FishLC50Predictor
from utils import prepare_data, evaluate_model, seed_torch
from visualization_utils import (
    load_trained_model,
    get_predictions
)

# 设置字体和样式
plt.rcParams['font.family'] = 'Times New Roman'
plt.rcParams['font.size'] = 12
plt.rcParams['axes.unicode_minus'] = False
sns.set_style("whitegrid")

def create_auc_plot(train_preds, train_true, test_preds, test_true, save_path='auc_comparison.png'):
    """创建AUC对比图"""
    
    # 创建单个图形
    fig, ax = plt.subplots(1, 1, figsize=(10, 8))
    
    # 计算ROC曲线
    train_fpr, train_tpr, _ = roc_curve(train_true, train_preds)
    train_auc = auc(train_fpr, train_tpr)
    
    test_fpr, test_tpr, _ = roc_curve(test_true, test_preds)
    test_auc = auc(test_fpr, test_tpr)
    
    # 绘制ROC曲线
    ax.plot(train_fpr, train_tpr, 'b-', linewidth=3, 
            label=f'Training Set - AUC = {train_auc:.4f}')
    ax.plot(test_fpr, test_tpr, 'r-', linewidth=3, 
            label=f'Test Set - AUC = {test_auc:.4f}')
    ax.plot([0, 1], [0, 1], 'k--', alpha=0.5, linewidth=2, label='Random Classifier')
    
    # 设置图形属性
    ax.set_xlabel('False Positive Rate', fontsize=14)
    ax.set_ylabel('True Positive Rate', fontsize=14)
    ax.set_title('ROC Curve: Model Performance Evaluation', fontsize=16, fontweight='bold')
    ax.legend(fontsize=12, loc='lower right')
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.show()
    
    return fig

def print_auc_summary(train_preds, train_true, test_preds, test_true):
    """打印AUC摘要报告"""
    
    print("=" * 60)
    print("MODEL PERFORMANCE - AUC ANALYSIS REPORT")
    print("=" * 60)
    
    # 计算AUC
    train_fpr, train_tpr, _ = roc_curve(train_true, train_preds)
    train_auc = auc(train_fpr, train_tpr)
    
    test_fpr, test_tpr, _ = roc_curve(test_true, test_preds)
    test_auc = auc(test_fpr, test_tpr)
    
    print(f"\n📊 AUC Performance:")
    print(f"Training Set AUC:  {train_auc:.4f}")
    print(f"Test Set AUC:      {test_auc:.4f}")
    print(f"AUC Difference:    {abs(train_auc - test_auc):.4f}")
    
    # 数据集信息
    print(f"\n📈 Dataset Information:")
    print(f"Training samples: {len(train_true)} (Positive: {np.sum(train_true)}, Negative: {np.sum(train_true == 0)})")
    print(f"Test samples: {len(test_true)} (Positive: {np.sum(test_true)}, Negative: {np.sum(test_true == 0)})")
    
    print("\n" + "=" * 60)

def main():
    """主函数"""
    # 设置参数
    seed = 42
    batch_size = 32
    dropout_rate = 0.4
    
    # 设置随机种子
    seed_torch(seed)
    
    print("🚀 开始加载数据和模型...")
    
    # 读取数据
    df = pd.read_excel('input_data_FishLC50.xlsx')
    smiles_data, graph_data, labels = prepare_data(df)
    
    # 划分数据集（与训练时保持一致）
    indices = torch.arange(len(df))
    train_idx, test_idx = train_test_split(indices, test_size=0.2, random_state=seed, 
                                          stratify=labels.numpy())
    
    # 创建数据加载器
    train_dataset = TensorDataset(smiles_data[train_idx], graph_data[train_idx], labels[train_idx])
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=False)
    
    test_dataset = TensorDataset(smiles_data[test_idx], graph_data[test_idx], labels[test_idx])
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
    
    # 加载训练好的模型
    try:
        model = load_trained_model('models/fish_lc50_predictor.pth', dropout_rate)
        print("✅ 模型加载成功!")
    except FileNotFoundError:
        print("❌ 未找到训练好的模型文件，请先运行main.py进行训练")
        return
    
    # 获取预测结果
    print("🔍 正在获取预测结果...")
    train_preds, train_true = get_predictions(model, train_loader)
    test_preds, test_true = get_predictions(model, test_loader)
    
    # 打印AUC摘要
    print_auc_summary(train_preds, train_true, test_preds, test_true)
    
    # 创建AUC可视化
    print("🎨 正在生成AUC图表...")
    
    # AUC对比图
    create_auc_plot(train_preds, train_true, test_preds, test_true, 
                   save_path='auc_comparison.png')
    
    print("✅ 可视化完成! 图片已保存为:")
    print("   - auc_comparison.png (AUC对比图)")

if __name__ == '__main__':
    main()