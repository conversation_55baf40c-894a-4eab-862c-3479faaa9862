import numpy as np
import pandas as pd
import torch
from model import FishLC50Predictor
from utils import smiles_to_sequence, smiles_to_graph

def predict_fish_lc50(smiles_list, model_path='models/fish_lc50_predictor.pth'):
    """
    预测FishLC50值
    
    Args:
        smiles_list: SMILES字符串列表
        model_path: 模型文件路径
    
    Returns:
        predictions: 预测概率列表
    """
    # 加载模型
    model = FishLC50Predictor()
    model.load_state_dict(torch.load(model_path, map_location='cpu'))
    model.eval()
    
    if torch.cuda.is_available():
        model = model.cuda()
    
    predictions = []
    
    with torch.no_grad():
        for smiles in smiles_list:
            # 准备输入数据
            smiles_seq = smiles_to_sequence(smiles)
            point_data = smiles_to_graph(smiles)
            
            # 转换为tensor
            smiles_tensor = torch.tensor([smiles_seq], dtype=torch.long)
            point_tensor = torch.tensor([point_data], dtype=torch.float32)
            
            if torch.cuda.is_available():
                smiles_tensor = smiles_tensor.cuda()
                point_tensor = point_tensor.cuda()
            
            # 预测
            outputs = model(smiles_tensor, point_tensor)
            probs = torch.softmax(outputs, dim=1)
            pred_prob = probs[0, 1].cpu().item()  # 获取正类概率
            
            predictions.append(pred_prob)
    
    return predictions


def main():
    # 读取输入数据
    df = pd.read_excel('input_data.xlsx')
    smiles_list = df['smiles'].tolist()
    true_labels = df['FishLC50'].tolist()
    
    # 进行预测
    try:
        predictions = predict_fish_lc50(smiles_list)
        
        # 创建结果DataFrame
        results_df = pd.DataFrame({
            'smiles': smiles_list,
            'true_label': true_labels,
            'predicted_prob': predictions,
            'predicted_label': [1 if p > 0.5 else 0 for p in predictions]
        })
        
        # 保存结果
        results_df.to_csv('prediction_results.csv', index=False)
        print(f"预测完成，结果已保存到 prediction_results.csv")
        print(f"预测了 {len(predictions)} 个样本")
        
        # 计算准确率
        correct = sum(1 for i in range(len(true_labels)) 
                     if (predictions[i] > 0.5) == true_labels[i])
        accuracy = correct / len(true_labels)
        print(f"预测准确率: {accuracy:.4f}")
        
    except FileNotFoundError:
        print("错误: 未找到训练好的模型文件。请先运行 main.py 进行训练。")
    except Exception as e:
        print(f"预测过程中出现错误: {e}")


if __name__ == '__main__':
    main()