#!/usr/bin/env python3
"""
简化分子可视化测试脚本
测试简洁的分子结构注意力可视化功能
"""

import torch
import numpy as np
import os

def test_simple_molecular_visualization():
    """测试简化的分子可视化功能"""
    print("=" * 60)
    print("简化分子结构注意力可视化测试")
    print("=" * 60)
    
    try:
        from model import FishLC50Predictor
        from interpretability.molecular_attention_visualizer import MolecularAttentionVisualizer
        
        # 创建模型和可视化器
        model = FishLC50Predictor(dropout_rate=0.4)
        model.eval()
        visualizer = MolecularAttentionVisualizer(model, 'cpu')
        
        print("✓ 分子可视化器初始化成功")
        
        # 测试分子列表
        test_molecules = [
            ("CCO", "乙醇"),
            ("c1ccccc1", "苯"),
            ("CCN(CC)CC", "三乙胺"),
            ("c1ccc(cc1)N", "苯胺"),
            ("CC(=O)O", "乙酸"),
            ("c1ccc(cc1)O", "苯酚"),
        ]
        
        # 创建结果目录
        os.makedirs('results/simple_test', exist_ok=True)
        
        print(f"\n🧪 开始生成 {len(test_molecules)} 个简化分子结构图...")
        
        for i, (smiles, name) in enumerate(test_molecules):
            print(f"\n{i+1}. {name} ({smiles})")
            
            # 解析分子结构
            atoms, bonds, coords = visualizer.parse_smiles_structure(smiles)
            print(f"   解析结果: {len(atoms)}个原子, {len(bonds)}个键")
            
            if len(atoms) > 0:
                # 创建模拟注意力权重
                attention_weights = np.random.random(len(atoms))
                # 让某些原子有更高的注意力
                if len(atoms) > 0:
                    attention_weights[0] = 0.8  # 第一个原子高注意力
                if len(atoms) > 2:
                    attention_weights[2] = 0.6  # 第三个原子中等注意力
                
                # 归一化
                attention_weights = attention_weights / np.sum(attention_weights)
                
                # 生成可视化
                save_path = f'results/simple_test/molecular_{i+1}_{name}.png'
                visualizer.visualize_molecular_attention(smiles, attention_weights, save_path)
                
                print(f"   ✓ 已生成: {save_path}")
                
                # 显示注意力权重信息
                high_attention_atoms = [j for j, w in enumerate(attention_weights) if w > 0.3]
                if high_attention_atoms:
                    print(f"   高注意力原子: {[atoms[j] + str(j+1) for j in high_attention_atoms]}")
            else:
                print(f"   ❌ 无法解析SMILES: {smiles}")
        
        print(f"\n✅ 简化分子可视化测试完成！")
        print("\n🎨 简化后的特点:")
        print("- 简洁的分子结构图显示")
        print("- 标准原子颜色（C=黑色, N=蓝色, O=红色等）")
        print("- 固定原子大小，不随注意力变化")
        print("- 高注意力原子用绿色圆圈标记")
        print("- 隐藏坐标轴，图形更简洁")
        print("- 黑色键线连接原子")
        print("- 显示所有非氢原子符号")
        
        print(f"\n📁 生成的文件位于: results/simple_test/")
        print("请查看生成的图像，应该类似您提供的参考图样式")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_smiles_parsing_detailed():
    """详细测试SMILES解析功能"""
    print("\n" + "=" * 60)
    print("SMILES解析详细测试")
    print("=" * 60)
    
    try:
        from interpretability.molecular_attention_visualizer import MolecularAttentionVisualizer
        from model import FishLC50Predictor
        
        model = FishLC50Predictor(dropout_rate=0.4)
        visualizer = MolecularAttentionVisualizer(model, 'cpu')
        
        test_molecules = [
            ("CCO", "乙醇 - 简单醇类"),
            ("c1ccccc1", "苯 - 芳香环"),
            ("c1ccc2c(c1)ccc3c2cccc3", "蒽 - 多环芳烃"),
            ("CCN(CC)CC", "三乙胺 - 含氮化合物"),
            ("CC(=O)O", "乙酸 - 羧酸"),
            ("c1ccc(cc1)N", "苯胺 - 芳香胺"),
            ("c1ccc(cc1)O", "苯酚 - 酚类"),
            ("CCc1ccccc1", "乙苯 - 烷基苯"),
        ]
        
        print("分子解析结果:")
        for smiles, description in test_molecules:
            atoms, bonds, coords = visualizer.parse_smiles_structure(smiles)
            print(f"  {description}")
            print(f"    SMILES: {smiles}")
            print(f"    原子: {len(atoms)}个 {atoms if len(atoms) <= 10 else atoms[:10] + ['...']}")
            print(f"    键: {len(bonds)}个")
            print(f"    坐标: {coords.shape if len(coords) > 0 else '无坐标'}")
            print()
        
        print("✓ SMILES解析详细测试完成")
        return True
        
    except Exception as e:
        print(f"❌ SMILES解析测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧬 简化分子结构可视化测试程序")
    
    # 运行测试
    test_results = []
    
    # 1. 测试简化分子可视化
    test_results.append(test_simple_molecular_visualization())
    
    # 2. 测试SMILES解析
    test_results.append(test_smiles_parsing_detailed())
    
    # 总结结果
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"通过测试: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！简化的分子可视化功能正常工作。")
        print("\n📋 简化后的改进:")
        print("✅ 移除了花哨的效果和复杂的配色")
        print("✅ 使用标准的原子颜色")
        print("✅ 固定原子大小，更加简洁")
        print("✅ 用简单的绿色圆圈标记高注意力原子")
        print("✅ 隐藏坐标轴，图形更清晰")
        print("✅ 只显示分子结构图，不显示柱状图")
    else:
        print("⚠️  部分测试失败，请检查错误信息。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
