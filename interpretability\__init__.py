# Interpretability package initialization
"""
双通道分子图神经网络可解释性分析包

包含以下模块:
- channel_analyzer: 通道重要性分析器
- molecular_attention_visualizer: 分子注意力可视化器  
- advanced_explainer: 高级可解释性分析器
"""

__version__ = "1.0.0"
__author__ = "DCGCN-3D Team"

# 导入主要类
from .channel_analyzer import ChannelAnalyzer
from .molecular_attention_visualizer import MolecularAttentionVisualizer
from .advanced_explainer import AdvancedExplainer

__all__ = [
    'ChannelAnalyzer',
    'MolecularAttentionVisualizer', 
    'AdvancedExplainer'
]
