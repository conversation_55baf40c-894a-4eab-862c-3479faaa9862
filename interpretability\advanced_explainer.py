import torch
import torch.nn.functional as F
import numpy as np
import matplotlib.pyplot as plt
from sklearn.linear_model import Ridge
from sklearn.metrics import r2_score
import random
from typing import Dict, List, Tuple, Optional
import warnings

class AdvancedExplainer:
    """
    高级可解释性分析器
    实现LIME、SHAP等先进的可解释性方法
    """
    
    def __init__(self, model, device='cpu'):
        """
        初始化高级可解释性分析器
        
        Args:
            model: 训练好的模型
            device: 计算设备
        """
        self.model = model
        self.device = device
        self.model.to(device)
        self.model.eval()
    
    def lime_explanation(self, smiles_tensor, point_tensor, n_samples=1000, n_features=10):
        """
        使用LIME方法解释模型预测
        
        Args:
            smiles_tensor: SMILES张量
            point_tensor: 3D点云张量
            n_samples: 采样数量
            n_features: 选择的特征数量
            
        Returns:
            dict: LIME解释结果
        """
        print("正在使用LIME方法进行模型解释...")
        
        # 移动数据到设备
        smiles_tensor = smiles_tensor.to(self.device).long()
        point_tensor = point_tensor.to(self.device).float()
        
        # 获取原始预测
        with torch.no_grad():
            original_output = self.model(smiles_tensor, point_tensor)
            original_probs = F.softmax(original_output, dim=1)
            target_class = torch.argmax(original_probs, dim=1).item()
        
        # 生成扰动样本
        perturbed_samples = []
        predictions = []
        feature_masks = []
        
        for _ in range(n_samples):
            # 创建特征掩码
            smiles_mask, point_mask = self._create_feature_mask(smiles_tensor, point_tensor)
            feature_masks.append((smiles_mask, point_mask))
            
            # 应用掩码创建扰动样本
            perturbed_smiles = smiles_tensor * smiles_mask.unsqueeze(0)
            perturbed_point = point_tensor * point_mask.unsqueeze(0).unsqueeze(-1)
            
            # 获取预测
            with torch.no_grad():
                perturbed_output = self.model(perturbed_smiles, perturbed_point)
                perturbed_probs = F.softmax(perturbed_output, dim=1)
                predictions.append(perturbed_probs[0, target_class].item())
            
            perturbed_samples.append((perturbed_smiles, perturbed_point))
        
        # 准备LIME回归数据
        X = self._prepare_lime_features(feature_masks, smiles_tensor, point_tensor)
        y = np.array(predictions)
        
        # 训练线性模型
        lime_model = Ridge(alpha=1.0)
        lime_model.fit(X, y)
        
        # 获取特征重要性
        feature_importance = lime_model.coef_
        
        # 计算解释质量
        y_pred = lime_model.predict(X)
        explanation_score = r2_score(y, y_pred)
        
        return {
            'feature_importance': feature_importance,
            'explanation_score': explanation_score,
            'target_class': target_class,
            'original_prediction': original_probs[0, target_class].item(),
            'method': 'LIME'
        }
    
    def _create_feature_mask(self, smiles_tensor, point_tensor):
        """创建特征掩码用于LIME扰动"""
        # SMILES掩码：随机保留一些token
        smiles_length = smiles_tensor.size(1)
        smiles_mask = torch.rand(smiles_length) > 0.3  # 70%概率保留
        
        # 点云掩码：随机保留一些原子
        n_atoms = point_tensor.size(1)
        point_mask = torch.rand(n_atoms) > 0.2  # 80%概率保留
        
        return smiles_mask.to(self.device), point_mask.to(self.device)
    
    def _prepare_lime_features(self, feature_masks, smiles_tensor, point_tensor):
        """准备LIME回归的特征矩阵"""
        n_samples = len(feature_masks)
        smiles_length = smiles_tensor.size(1)
        n_atoms = point_tensor.size(1)
        
        # 创建特征矩阵
        X = np.zeros((n_samples, smiles_length + n_atoms))
        
        for i, (smiles_mask, point_mask) in enumerate(feature_masks):
            # SMILES特征
            X[i, :smiles_length] = smiles_mask.cpu().numpy()
            # 点云特征
            X[i, smiles_length:] = point_mask.cpu().numpy()
        
        return X
    
    def shap_explanation(self, smiles_tensor, point_tensor, n_samples=100):
        """
        使用SHAP方法解释模型预测（简化实现）
        
        Args:
            smiles_tensor: SMILES张量
            point_tensor: 3D点云张量
            n_samples: 采样数量
            
        Returns:
            dict: SHAP解释结果
        """
        print("正在使用SHAP方法进行模型解释...")
        
        smiles_tensor = smiles_tensor.to(self.device).long()
        point_tensor = point_tensor.to(self.device).float()
        
        # 获取原始预测
        with torch.no_grad():
            original_output = self.model(smiles_tensor, point_tensor)
            original_probs = F.softmax(original_output, dim=1)
            target_class = torch.argmax(original_probs, dim=1).item()
        
        # 创建基线（零输入）
        smiles_baseline = torch.zeros_like(smiles_tensor)
        point_baseline = torch.zeros_like(point_tensor)
        
        # 计算SHAP值（简化版本）
        smiles_shap_values = self._compute_shap_values(
            smiles_tensor, smiles_baseline, point_tensor, target_class, 'smiles', n_samples)
        point_shap_values = self._compute_shap_values(
            point_tensor, point_baseline, smiles_tensor, target_class, 'point', n_samples)
        
        return {
            'smiles_shap_values': smiles_shap_values,
            'point_shap_values': point_shap_values,
            'target_class': target_class,
            'original_prediction': original_probs[0, target_class].item(),
            'method': 'SHAP'
        }
    
    def _compute_shap_values(self, target_tensor, baseline_tensor, other_tensor, 
                           target_class, tensor_type, n_samples):
        """计算SHAP值（简化实现）"""
        shap_values = []
        
        if tensor_type == 'smiles':
            n_features = target_tensor.size(1)
        else:  # point
            n_features = target_tensor.size(1)
        
        for feature_idx in range(n_features):
            # 计算包含和不包含该特征的预测差异
            marginal_contributions = []
            
            for _ in range(n_samples // n_features):
                # 随机选择其他特征的子集
                feature_subset = random.sample(range(n_features), 
                                             random.randint(1, n_features-1))
                
                # 创建两个版本：包含和不包含当前特征
                if tensor_type == 'smiles':
                    tensor_with = self._create_masked_smiles(
                        target_tensor, baseline_tensor, feature_subset + [feature_idx])
                    tensor_without = self._create_masked_smiles(
                        target_tensor, baseline_tensor, feature_subset)
                    
                    with torch.no_grad():
                        pred_with = F.softmax(self.model(tensor_with, other_tensor), dim=1)
                        pred_without = F.softmax(self.model(tensor_without, other_tensor), dim=1)
                else:  # point
                    tensor_with = self._create_masked_point(
                        target_tensor, baseline_tensor, feature_subset + [feature_idx])
                    tensor_without = self._create_masked_point(
                        target_tensor, baseline_tensor, feature_subset)
                    
                    with torch.no_grad():
                        pred_with = F.softmax(self.model(other_tensor, tensor_with), dim=1)
                        pred_without = F.softmax(self.model(other_tensor, tensor_without), dim=1)
                
                # 计算边际贡献
                contribution = (pred_with[0, target_class] - pred_without[0, target_class]).item()
                marginal_contributions.append(contribution)
            
            # 平均边际贡献作为SHAP值
            shap_values.append(np.mean(marginal_contributions))
        
        return np.array(shap_values)
    
    def _create_masked_smiles(self, original, baseline, feature_indices):
        """创建掩码SMILES张量"""
        masked = baseline.clone()
        for idx in feature_indices:
            if idx < original.size(1):
                masked[0, idx] = original[0, idx]
        return masked
    
    def _create_masked_point(self, original, baseline, feature_indices):
        """创建掩码点云张量"""
        masked = baseline.clone()
        for idx in feature_indices:
            if idx < original.size(1):
                masked[0, idx, :] = original[0, idx, :]
        return masked
    
    def counterfactual_explanation(self, smiles_tensor, point_tensor, target_class=None):
        """
        生成反事实解释
        
        Args:
            smiles_tensor: SMILES张量
            point_tensor: 3D点云张量
            target_class: 目标类别（如果为None，则使用相反类别）
            
        Returns:
            dict: 反事实解释结果
        """
        print("正在生成反事实解释...")
        
        smiles_tensor = smiles_tensor.to(self.device).long()
        point_tensor = point_tensor.to(self.device).float()
        
        # 获取原始预测
        with torch.no_grad():
            original_output = self.model(smiles_tensor, point_tensor)
            original_probs = F.softmax(original_output, dim=1)
            original_class = torch.argmax(original_probs, dim=1).item()
        
        # 设置目标类别
        if target_class is None:
            target_class = 1 - original_class  # 假设是二分类
        
        # 使用梯度上升寻找反事实样本
        counterfactual_point = point_tensor.clone().requires_grad_(True)
        optimizer = torch.optim.Adam([counterfactual_point], lr=0.01)
        
        best_counterfactual = None
        best_confidence = 0
        
        for step in range(100):
            optimizer.zero_grad()
            
            output = self.model(smiles_tensor, counterfactual_point)
            probs = F.softmax(output, dim=1)
            
            # 最大化目标类别的概率
            loss = -probs[0, target_class]
            loss.backward()
            optimizer.step()
            
            # 检查是否达到目标
            current_confidence = probs[0, target_class].item()
            if current_confidence > best_confidence:
                best_confidence = current_confidence
                best_counterfactual = counterfactual_point.clone().detach()
            
            if current_confidence > 0.7:  # 如果置信度足够高就停止
                break
        
        # 计算变化
        if best_counterfactual is not None:
            changes = torch.abs(best_counterfactual - point_tensor).sum(dim=-1).cpu().numpy()[0]
        else:
            changes = np.zeros(point_tensor.size(1))
        
        return {
            'counterfactual_found': best_counterfactual is not None,
            'target_class': target_class,
            'original_class': original_class,
            'counterfactual_confidence': best_confidence,
            'changes': changes,
            'method': 'Counterfactual'
        }
    
    def visualize_advanced_explanations(self, explanations, save_path):
        """
        可视化高级解释结果
        
        Args:
            explanations: 解释结果字典
            save_path: 保存路径
        """
        print("正在生成高级解释可视化...")
        
        n_methods = len(explanations)
        fig, axes = plt.subplots(2, n_methods, figsize=(6*n_methods, 12))
        if n_methods == 1:
            axes = axes.reshape(2, 1)
        
        fig.suptitle('高级可解释性分析结果', fontsize=16, fontweight='bold')
        
        for i, (method, result) in enumerate(explanations.items()):
            if method == 'LIME':
                self._plot_lime_results(axes[0, i], axes[1, i], result)
            elif method == 'SHAP':
                self._plot_shap_results(axes[0, i], axes[1, i], result)
            elif method == 'Counterfactual':
                self._plot_counterfactual_results(axes[0, i], axes[1, i], result)
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
        print(f"高级解释可视化已保存到: {save_path}")
        
        return fig
    
    def _plot_lime_results(self, ax1, ax2, result):
        """绘制LIME结果"""
        importance = result['feature_importance']
        
        # 特征重要性柱状图
        ax1.bar(range(len(importance)), importance, alpha=0.7)
        ax1.set_title(f'LIME特征重要性 (R²={result["explanation_score"]:.3f})', fontweight='bold')
        ax1.set_xlabel('特征索引')
        ax1.set_ylabel('重要性')
        ax1.grid(True, alpha=0.3)
        
        # 信息文本
        ax2.axis('off')
        info_text = f"""
LIME解释结果

📊 解释质量 (R²): {result['explanation_score']:.4f}
🎯 目标类别: {result['target_class']}
📈 原始预测: {result['original_prediction']:.4f}

🔍 方法说明:
• LIME通过局部线性近似解释模型
• 生成扰动样本训练线性模型
• 特征重要性反映局部影响

📈 结果解读:
• 正值: 增加预测概率
• 负值: 降低预测概率
• 绝对值: 影响强度
        """
        
        ax2.text(0.05, 0.95, info_text, transform=ax2.transAxes, 
                fontsize=10, verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle="round,pad=0.5", facecolor="lightgreen", alpha=0.8))
    
    def _plot_shap_results(self, ax1, ax2, result):
        """绘制SHAP结果"""
        smiles_shap = result['smiles_shap_values']
        point_shap = result['point_shap_values']
        
        # SHAP值柱状图
        x_pos = np.arange(len(smiles_shap) + len(point_shap))
        shap_values = np.concatenate([smiles_shap, point_shap])
        colors = ['blue'] * len(smiles_shap) + ['red'] * len(point_shap)
        
        ax1.bar(x_pos, shap_values, color=colors, alpha=0.7)
        ax1.set_title('SHAP值分布', fontweight='bold')
        ax1.set_xlabel('特征索引')
        ax1.set_ylabel('SHAP值')
        ax1.axhline(y=0, color='black', linestyle='-', alpha=0.3)
        ax1.grid(True, alpha=0.3)
        
        # 信息文本
        ax2.axis('off')
        info_text = f"""
SHAP解释结果

🎯 目标类别: {result['target_class']}
📈 原始预测: {result['original_prediction']:.4f}

📊 SMILES特征:
• 平均SHAP值: {np.mean(smiles_shap):.4f}
• 最大影响: {np.max(np.abs(smiles_shap)):.4f}

📊 3D结构特征:
• 平均SHAP值: {np.mean(point_shap):.4f}
• 最大影响: {np.max(np.abs(point_shap)):.4f}

🔍 方法说明:
• SHAP基于博弈论的公平分配
• 蓝色: SMILES特征
• 红色: 3D结构特征
        """
        
        ax2.text(0.05, 0.95, info_text, transform=ax2.transAxes, 
                fontsize=10, verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle="round,pad=0.5", facecolor="lightyellow", alpha=0.8))
    
    def _plot_counterfactual_results(self, ax1, ax2, result):
        """绘制反事实结果"""
        changes = result['changes']
        
        # 变化量柱状图
        ax1.bar(range(len(changes)), changes, alpha=0.7, color='purple')
        ax1.set_title('反事实分析 - 特征变化量', fontweight='bold')
        ax1.set_xlabel('原子索引')
        ax1.set_ylabel('变化量')
        ax1.grid(True, alpha=0.3)
        
        # 信息文本
        ax2.axis('off')
        found_text = "成功" if result['counterfactual_found'] else "失败"
        info_text = f"""
反事实解释结果

🎯 反事实生成: {found_text}
📊 原始类别: {result['original_class']}
🎯 目标类别: {result['target_class']}
📈 反事实置信度: {result['counterfactual_confidence']:.4f}

📊 变化统计:
• 总变化量: {np.sum(changes):.4f}
• 平均变化: {np.mean(changes):.4f}
• 最大变化: {np.max(changes):.4f}

🔍 方法说明:
• 寻找最小变化使预测翻转
• 紫色柱状图显示各原子变化量
• 变化量越大表示该原子越重要
        """
        
        ax2.text(0.05, 0.95, info_text, transform=ax2.transAxes, 
                fontsize=10, verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle="round,pad=0.5", facecolor="lightcoral", alpha=0.8))
