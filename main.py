import pandas as pd
import torch
import torch.nn as nn
from torch.utils.data import TensorDataset, DataLoader
from sklearn.model_selection import train_test_split

from model import FishNOEC0Predictor
from utils import prepare_data, evaluate_model, seed_torch

# 训练参数 + 正则化参数
seed = 42
lr = 0.001
batch_size = 32
epoch = 100
weight_decay = 1e-4
dropout_rate = 0.4  # 全局dropout率


def main():
    """主训练函数 - 包含正则化技术"""
    # 设置随机种子
    seed_torch(seed)
    
    # 读取数据
    df = pd.read_excel('input_data_FishNOEC.xlsx')
    print(f"数据集大小: {len(df)}")
    print(f"标签分布: {df['FishNOEC'].value_counts().to_dict()}")
    
    # 准备数据
    smiles_data, graph_data, labels = prepare_data(df)
    
    # 划分训练集和测试集
    indices = torch.arange(len(df))
    train_idx, test_idx = train_test_split(indices, test_size=0.2, random_state=seed, 
                                          stratify=labels.numpy())
    
    train_smiles = smiles_data[train_idx]
    train_graph = graph_data[train_idx]
    train_labels = labels[train_idx]
    
    test_smiles = smiles_data[test_idx]
    test_graph = graph_data[test_idx]
    test_labels = labels[test_idx]
    
    # 创建数据加载器
    train_dataset = TensorDataset(train_smiles, train_graph, train_labels)
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    
    test_dataset = TensorDataset(test_smiles, test_graph, test_labels)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
    
    # 初始化模型 (传入dropout参数)
    model = FishNOECPredictor(dropout_rate=dropout_rate)
    if torch.cuda.is_available():
        model = model.cuda()
        print("使用GPU训练")
    else:
        print("使用CPU训练")
    
    # 损失函数和优化器 (添加权重衰减)
    criterion = nn.CrossEntropyLoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=lr, weight_decay=weight_decay)
    
    # 学习率调度器 (减少过拟合)
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='max', factor=0.8, patience=10, verbose=True, min_lr=1e-6
    )
    
    # 训练模型
    best_auc = 0
    best_balanced_acc = 0
    best_model_state = None
    
    # 早停机制参数
    patience = 20  # 连续20个epoch没有改善就停止
    no_improve_count = 0
    best_combined_score = 0  # 综合评分 (AUC + Balanced_ACC) / 2
    
    print("开始训练...")
    print(f"正则化设置: Dropout={dropout_rate}, Weight_Decay={weight_decay}")
    
    for epoch_idx in range(epoch):
        model.train()
        total_loss = 0
        
        # 训练循环
        for batch_idx, (smiles_batch, graph_batch, labels_batch) in enumerate(train_loader):
            if torch.cuda.is_available():
                smiles_batch = smiles_batch.cuda()
                graph_batch = graph_batch.cuda()
                labels_batch = labels_batch.cuda()
            
            optimizer.zero_grad()
            outputs = model(smiles_batch, graph_batch)
            loss = criterion(outputs, labels_batch)
            loss.backward()
            optimizer.step()
            
            total_loss += loss.item()
        
        # 每个epoch都进行验证和打印
        model.eval()
        test_preds = []
        test_true = []
        
        with torch.no_grad():
            for smiles_batch, graph_batch, labels_batch in test_loader:
                if torch.cuda.is_available():
                    smiles_batch = smiles_batch.cuda()
                    graph_batch = graph_batch.cuda()
                
                outputs = model(smiles_batch, graph_batch)
                probs = torch.softmax(outputs, dim=1)[:, 1]
                
                test_preds.extend(probs.cpu().numpy())
                test_true.extend(labels_batch.numpy())
        
        # 评估性能
        auc_score, aupr_score, balanced_acc, sen, spe = evaluate_model(test_preds, test_true)
        
        print(f'Epoch {epoch_idx+1:3d}/{epoch}: Loss={total_loss/len(train_loader):.4f}, '
              f'AUC={auc_score:.4f}, AUPR={aupr_score:.4f}, '
              f'Balanced_ACC={balanced_acc:.4f}, SEN={sen:.4f}, SPE={spe:.4f}')
        
        # 学习率调度器更新 (基于综合评分)
        current_combined_score = (auc_score + balanced_acc) / 2
        scheduler.step(current_combined_score)
        
        # 早停机制和最佳模型保存
        if current_combined_score > best_combined_score:
            best_combined_score = current_combined_score
            best_auc = auc_score
            best_balanced_acc = balanced_acc
            best_model_state = model.state_dict().copy()
            no_improve_count = 0
            current_lr = optimizer.param_groups[0]['lr']
            print(f'  ⭐ 新的最佳模型! AUC: {best_auc:.4f}, Balanced_ACC: {best_balanced_acc:.4f}, 综合评分: {best_combined_score:.4f}, LR: {current_lr:.2e}')
        else:
            no_improve_count += 1
            if no_improve_count >= patience:
                print(f'\n🛑 早停触发! 连续{patience}个epoch无改善，在第{epoch_idx+1}个epoch停止训练')
                break
            elif no_improve_count % 5 == 0:
                current_lr = optimizer.param_groups[0]['lr']
                print(f'  ⏳ 已连续{no_improve_count}个epoch无改善 (耐心值: {patience}), 当前LR: {current_lr:.2e}')
    
    # 保存最佳模型并进行最终评估
    if best_model_state is not None:
        torch.save(best_model_state, 'models/FishNOEC_predictor.pth')
        
        if no_improve_count >= patience:
            print(f'训练因早停机制结束，最佳模型来自第{epoch_idx+1-no_improve_count}个epoch')
        else:
            print(f'训练正常完成{epoch}个epoch')
        
        # 加载最佳模型进行最终评估
        model.load_state_dict(best_model_state)
        model.eval()
        
        # 测试集评估
        final_test_preds = []
        final_test_true = []
        
        print(f'\n正在进行最终测试集评估...')
        with torch.no_grad():
            for smiles_batch, graph_batch, labels_batch in test_loader:
                if torch.cuda.is_available():
                    smiles_batch = smiles_batch.cuda()
                    graph_batch = graph_batch.cuda()
                
                outputs = model(smiles_batch, graph_batch)
                probs = torch.softmax(outputs, dim=1)[:, 1]
                
                final_test_preds.extend(probs.cpu().numpy())
                final_test_true.extend(labels_batch.numpy())
        
        # 训练集评估
        final_train_preds = []
        final_train_true = []
        
        print(f'正在进行最终训练集评估...')
        with torch.no_grad():
            for smiles_batch, graph_batch, labels_batch in train_loader:
                if torch.cuda.is_available():
                    smiles_batch = smiles_batch.cuda()
                    graph_batch = graph_batch.cuda()
                
                outputs = model(smiles_batch, graph_batch)
                probs = torch.softmax(outputs, dim=1)[:, 1]
                
                final_train_preds.extend(probs.cpu().numpy())
                final_train_true.extend(labels_batch.numpy())
        
        # 计算最终指标
        final_test_auc, final_test_aupr, final_test_balanced_acc, final_test_sen, final_test_spe = evaluate_model(final_test_preds, final_test_true)
        final_train_auc, final_train_aupr, final_train_balanced_acc, final_train_sen, final_train_spe = evaluate_model(final_train_preds, final_train_true)
        
        # 打印对比结果
        print(f'\n=== 最终模型性能对比 ===')
        print(f'{"指标":<12} {"训练集":<10} {"测试集":<10} {"差值":<10}')
        print(f'{"-"*42}')
        print(f'{"AUC":<12} {final_train_auc:<10.4f} {final_test_auc:<10.4f} {abs(final_train_auc-final_test_auc):<10.4f}')
        print(f'{"AUPR":<12} {final_train_aupr:<10.4f} {final_test_aupr:<10.4f} {abs(final_train_aupr-final_test_aupr):<10.4f}')
        print(f'{"平衡准确率":<12} {final_train_balanced_acc:<10.4f} {final_test_balanced_acc:<10.4f} {abs(final_train_balanced_acc-final_test_balanced_acc):<10.4f}')
        print(f'{"敏感性":<12} {final_train_sen:<10.4f} {final_test_sen:<10.4f} {abs(final_train_sen-final_test_sen):<10.4f}')
        print(f'{"特异性":<12} {final_train_spe:<10.4f} {final_test_spe:<10.4f} {abs(final_train_spe-final_test_spe):<10.4f}')
    else:
        print("警告: 未找到最佳模型，请检查训练过程")


if __name__ == '__main__':
    main()