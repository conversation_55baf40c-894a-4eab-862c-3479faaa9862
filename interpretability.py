import torch
import torch.nn.functional as F
import numpy as np
import matplotlib.pyplot as plt
from torch.utils.data import DataLoader, TensorDataset
import pandas as pd
import os
# 导入自定义模块
from model import FishLC50Predictor
from interpretability.channel_analyzer import ChannelAnalyzer
from interpretability.molecular_attention_visualizer import MolecularAttentionVisualizer

class SimplifiedInterpretabilityAnalysis:
    def __init__(self, model_path, data_path, device='cpu'):    
        self.device = device
        self.model_path = model_path
        self.data_path = data_path
        
        # 加载模型
        self.model = self._load_model()
        
        # 加载数据
        self.data = self._load_data()
          # 初始化分析器（只有在模型加载成功时）
        if self.model is not None:
            self.channel_analyzer = ChannelAnalyzer(self.model, device)
            self.attention_explainer = MolecularAttentionVisualizer(self.model, device)
        else:
            self.channel_analyzer = None
            self.attention_explainer = None
            print("警告: 模型加载失败，分析器未初始化")
        
        # 创建结果目录
        os.makedirs('results', exist_ok=True)
    
    def _load_model(self):
        """加载训练好的模型"""
        try:
            # 检查模型文件是否存在
            if not os.path.exists(self.model_path):
                print(f"模型文件不存在: {self.model_path}")
                print("将使用随机初始化的模型进行演示...")
                model = FishLC50Predictor(dropout_rate=0.4)
                model.to(self.device)
                model.eval()
                return model
            
            model = FishLC50Predictor(dropout_rate=0.4)
            
            # 加载模型权重
            checkpoint = torch.load(self.model_path, map_location=self.device)
            if 'model_state_dict' in checkpoint:
                model.load_state_dict(checkpoint['model_state_dict'])
            else:
                model.load_state_dict(checkpoint)
            
            model.to(self.device)
            model.eval()
            print("模型加载成功")
            return model
            
        except Exception as e:
            print(f"模型加载失败: {e}")
            print("将使用随机初始化的模型进行演示...")
            try:
                model = FishLC50Predictor(dropout_rate=0.4)
                model.to(self.device)
                model.eval()
                return model
            except Exception as e2:
                print(f"创建模型失败: {e2}")
                return None
    
    def _load_data(self):
        """加载测试数据"""
        try:
            # 加载数据
            data = pd.read_excel(self.data_path)
            
            # 提取SMILES和标签
            smiles_list = data['smiles'].tolist()
            labels = data['FishLC50'].tolist()
            
            # 简单的数据预处理
            smiles_tensors, point_tensors, processed_labels = self._preprocess_data(smiles_list, labels)
            
            return {
                'smiles_list': smiles_list,
                'smiles_tensors': smiles_tensors,
                'point_tensors': point_tensors,
                'labels': processed_labels
            }
            
        except Exception as e:
            print(f"数据加载失败: {e}")
            # 返回模拟数据用于测试
            return self._create_dummy_data()
    
    def _preprocess_data(self, smiles_list, labels):
        """简单的数据预处理"""
        print("正在预处理数据...")
        
        # 创建简单的SMILES字符到索引的映射
        char_to_idx = {}
        idx = 1  # 0保留给padding
        
        # 构建词汇表
        for smiles in smiles_list:
            for char in smiles:
                if char not in char_to_idx:
                    char_to_idx[char] = idx
                    idx += 1
        
        # 将SMILES转换为张量
        max_len = 100
        smiles_tensors = []
        
        for smiles in smiles_list:
            # 将SMILES字符串转换为索引序列
            indices = [char_to_idx.get(char, 0) for char in smiles]
            # 截断或填充到固定长度
            if len(indices) > max_len:
                indices = indices[:max_len]
            else:
                indices.extend([0] * (max_len - len(indices)))
            
            smiles_tensors.append(indices)
        
        smiles_tensors = torch.tensor(smiles_tensors, dtype=torch.long)
        
        # 生成模拟的3D点云数据
        batch_size = len(smiles_list)
        point_tensors = []
        
        for i in range(batch_size):
            # 距离矩阵 (50x50)
            distance_matrix = torch.randn(50, 50).abs()
            # 连接矩阵 (50x50) 
            connection_matrix = torch.randint(0, 2, (50, 50)).float()
            # 原子类型 (50,) - 确保在正确范围内
            atom_types = torch.randint(0, 79, (50,)).float()
            
            # 组合特征 [50, 101]
            point_features = torch.cat([
                distance_matrix,  # [50, 50]
                connection_matrix,  # [50, 50] 
                atom_types.unsqueeze(1)  # [50, 1]
            ], dim=1)
            
            point_tensors.append(point_features)
        
        point_tensors = torch.stack(point_tensors)
        
        # 处理标签
        median_value = np.median(labels)
        binary_labels = [1 if label > median_value else 0 for label in labels]
        processed_labels = torch.tensor(binary_labels, dtype=torch.long)
        
        print(f"预处理完成: {len(smiles_list)}个样本, 词汇表大小: {len(char_to_idx)}")
        
        return smiles_tensors, point_tensors, processed_labels
    
    def _create_dummy_data(self):
        """创建模拟数据用于测试"""
        print("使用模拟数据进行测试...")
        
        batch_size = 20
        smiles_list = ['CCO', 'CC(C)O', 'CCCO', 'CC(C)(C)O'] * (batch_size // 4 + 1)
        smiles_list = smiles_list[:batch_size]
        
        # 确保张量维度正确
        smiles_tensors = torch.randint(1, 65, (batch_size, 100))
        
        # 生成正确格式的3D点云数据
        point_tensors = []
        for i in range(batch_size):
            # 距离矩阵 (50x50)
            distance_matrix = torch.randn(50, 50).abs()
            # 连接矩阵 (50x50)
            connection_matrix = torch.randint(0, 2, (50, 50)).float()
            # 原子类型 (50,) - 确保在0-78范围内
            atom_types = torch.randint(0, 79, (50,)).float()
            
            # 组合特征 [50, 101]
            point_features = torch.cat([
                distance_matrix,  # [50, 50]
                connection_matrix,  # [50, 50]
                atom_types.unsqueeze(1)  # [50, 1]
            ], dim=1)
            
            point_tensors.append(point_features)
        
        point_tensors = torch.stack(point_tensors)
        labels = torch.randint(0, 2, (batch_size,))
        
        return {
            'smiles_list': smiles_list,
            'smiles_tensors': smiles_tensors,
            'point_tensors': point_tensors,
            'labels': labels
        }
    
    def run_channel_analysis(self):
        """运行通道重要性分析"""
        print("运行通道重要性分析...")
        
        # 创建数据加载器
        dataset = TensorDataset(
            self.data['smiles_tensors'][:20],
            self.data['point_tensors'][:20],
            self.data['labels'][:20]
        )
        data_loader = DataLoader(dataset, batch_size=4, shuffle=False)
        
        # 计算通道重要性
        importance_results = self.channel_analyzer.compute_channel_importance(data_loader)
        
        # 可视化结果
        self.channel_analyzer.visualize_channel_importance(
            importance_results, 
            save_path='results/channel_importance.png'
        )
        
        return importance_results
    
    def run_molecular_attention_analysis(self):
        """运行分子注意力可视化分析"""
        print("运行分子注意力可视化分析...")
        
        # 选择几个代表性样本进行分析
        sample_indices = [0, 5, 10, 15]
        
        for i, idx in enumerate(sample_indices):
            smiles = self.data['smiles_list'][idx]
            smiles_tensor = self.data['smiles_tensors'][idx]
            point_tensor = self.data['point_tensors'][idx]
            
            print(f"分析样本 {i+1}: {smiles}")
            
            # 提取注意力权重
            attention_data = self.attention_explainer.extract_attention_weights(
                smiles_tensor.unsqueeze(0),
                point_tensor.unsqueeze(0)
            )
            
            # 只生成分子注意力可视化
            self.attention_explainer.visualize_molecular_attention(
                smiles,
                attention_data['attention_weights'],
                save_path=f'results/molecular_attention_sample_{i+1}.png'
            )
        
        return {"molecular_attention_generated": True}
    


    def run_complete_analysis(self):
        """运行完整的可解释性分析 - 只包含两个核心功能"""
        print("开始可解释性分析...")

        # 检查模型是否加载成功
        if self.model is None:
            print("错误: 模型未加载成功，无法进行分析")
            return None

        if self.channel_analyzer is None or self.attention_explainer is None:
            print("错误: 分析器未初始化，无法进行分析")
            return None

        results = {}

        # 1. 通道重要性分析
        print("\n1. 通道重要性分析")
        channel_results = self.run_channel_analysis()
        results['channel_analysis'] = channel_results

        # 2. 分子结构注意力可视化
        print("\n2. 分子结构注意力可视化")
        attention_results = self.run_molecular_attention_analysis()
        results['molecular_attention'] = attention_results

        # 生成简化报告
        self._generate_report(results)

        print("\n可解释性分析完成！结果已保存到 results/ 目录")
        print("生成的文件:")
        print("- channel_importance.png (通道重要性对比可视化)")
        print("- molecular_attention_sample_1.png ~ molecular_attention_sample_4.png (简洁的分子结构注意力可视化)")
        print("- interpretability_report.txt (分析报告)")
        print("\n🎨 分子可视化特点:")
        print("- 简洁的分子结构图显示")
        print("- 标准原子颜色 (C=黑色, N=蓝色, O=红色等)")
        print("- 高注意力原子用绿色圆圈标记")
        print("- 类似您提供的参考图样式")

        return results
    
    def _generate_report(self, results):
        """生成可解释性分析报告"""
        report_path = 'results/interpretability_report.txt'

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("=== 鱼类LC50毒性预测模型可解释性分析报告 ===\n\n")

            # 通道重要性分析
            if 'channel_analysis' in results:
                channel_data = results['channel_analysis']
                f.write("1. 通道重要性分析 (梯度归因法)\n")
                f.write(f"SMILES通道重要性: {channel_data['smiles_importance']:.4f}\n")
                f.write(f"3D结构通道重要性: {channel_data['point_importance']:.4f}\n")

                total_importance = channel_data['smiles_importance'] + channel_data['point_importance']
                if total_importance > 0:
                    smiles_contrib = channel_data['smiles_importance'] / total_importance * 100
                    point_contrib = channel_data['point_importance'] / total_importance * 100
                    f.write(f"SMILES通道贡献: {smiles_contrib:.1f}%\n")
                    f.write(f"3D结构通道贡献: {point_contrib:.1f}%\n")
                f.write("说明: 数值越大表示该通道对模型预测的影响越大\n")
                f.write("\n")

            # 分子结构注意力分析结果
            if 'molecular_attention' in results:
                f.write("2. 分子结构注意力可视化\n")
                f.write("已生成4个样本的简洁分子结构注意力可视化图像\n")
                f.write("文件: molecular_attention_sample_1.png ~ molecular_attention_sample_4.png\n")
                f.write("可视化特点:\n")
                f.write("- 简洁的分子结构图显示\n")
                f.write("- 标准原子颜色 (C=黑色, N=蓝色, O=红色等)\n")
                f.write("- 高注意力原子用绿色圆圈标记\n")
                f.write("- 黑色键线连接原子\n")
                f.write("- 类似标准化学结构图样式\n")
                f.write("\n")

            # 分析方法说明
            f.write("3. 分析方法说明\n")
            f.write("- 通道重要性: 使用梯度归因法计算SMILES和3D结构通道的相对贡献\n")
            f.write("- 分子结构注意力: 基于模型梯度计算的注意力权重，识别重要原子\n")
            f.write("- 可视化方式: 简洁的标准化学结构图，绿色圆圈标记高注意力原子\n")
            f.write("- 设计理念: 类似标准化学教科书中的分子结构图样式\n")
            f.write("\n")

            f.write("分析完成时间: " + str(pd.Timestamp.now()) + "\n")

def main():
    # 配置参数
    model_path = 'models/FishLC50_predictor.pth'  # 模型路径
    data_path = 'input_data_FishLC50.xlsx'  # 数据路径
    device = 'cpu'  # 强制使用CPU避免CUDA问题
    
    print(f"使用设备: {device}")
    
    # 创建分析实例
    analysis = SimplifiedInterpretabilityAnalysis(model_path, data_path, device)
    
    # 运行完整分析
    results = analysis.run_complete_analysis()
    
    return results

if __name__ == "__main__":
    main()