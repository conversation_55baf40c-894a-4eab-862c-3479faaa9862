import torch
import torch.nn.functional as F
import numpy as np
import matplotlib.pyplot as plt
from torch.utils.data import DataLoader, TensorDataset
import pandas as pd
import os
# 导入自定义模块
from model import FishLC50Predictor
from interpretability.channel_analyzer import ChannelAnalyzer
from interpretability.molecular_attention_visualizer import MolecularAttentionVisualizer
from interpretability.advanced_explainer import AdvancedExplainer

class SimplifiedInterpretabilityAnalysis:
    def __init__(self, model_path, data_path, device='cpu'):    
        self.device = device
        self.model_path = model_path
        self.data_path = data_path
        
        # 加载模型
        self.model = self._load_model()
        
        # 加载数据
        self.data = self._load_data()
          # 初始化分析器（只有在模型加载成功时）
        if self.model is not None:
            self.channel_analyzer = ChannelAnalyzer(self.model, device)
            self.attention_explainer = MolecularAttentionVisualizer(self.model, device)
            self.advanced_explainer = AdvancedExplainer(self.model, device)
        else:
            self.channel_analyzer = None
            self.attention_explainer = None
            self.advanced_explainer = None
            print("警告: 模型加载失败，分析器未初始化")
        
        # 创建结果目录
        os.makedirs('results', exist_ok=True)
    
    def _load_model(self):
        """加载训练好的模型"""
        try:
            # 检查模型文件是否存在
            if not os.path.exists(self.model_path):
                print(f"模型文件不存在: {self.model_path}")
                print("将使用随机初始化的模型进行演示...")
                model = FishLC50Predictor(dropout_rate=0.4)
                model.to(self.device)
                model.eval()
                return model
            
            model = FishLC50Predictor(dropout_rate=0.4)
            
            # 加载模型权重
            checkpoint = torch.load(self.model_path, map_location=self.device)
            if 'model_state_dict' in checkpoint:
                model.load_state_dict(checkpoint['model_state_dict'])
            else:
                model.load_state_dict(checkpoint)
            
            model.to(self.device)
            model.eval()
            print("模型加载成功")
            return model
            
        except Exception as e:
            print(f"模型加载失败: {e}")
            print("将使用随机初始化的模型进行演示...")
            try:
                model = FishLC50Predictor(dropout_rate=0.4)
                model.to(self.device)
                model.eval()
                return model
            except Exception as e2:
                print(f"创建模型失败: {e2}")
                return None
    
    def _load_data(self):
        """加载测试数据"""
        try:
            # 加载数据
            data = pd.read_excel(self.data_path)
            
            # 提取SMILES和标签
            smiles_list = data['smiles'].tolist()
            labels = data['FishLC50'].tolist()
            
            # 简单的数据预处理
            smiles_tensors, point_tensors, processed_labels = self._preprocess_data(smiles_list, labels)
            
            return {
                'smiles_list': smiles_list,
                'smiles_tensors': smiles_tensors,
                'point_tensors': point_tensors,
                'labels': processed_labels
            }
            
        except Exception as e:
            print(f"数据加载失败: {e}")
            # 返回模拟数据用于测试
            return self._create_dummy_data()
    
    def _preprocess_data(self, smiles_list, labels):
        """简单的数据预处理"""
        print("正在预处理数据...")
        
        # 创建简单的SMILES字符到索引的映射
        char_to_idx = {}
        idx = 1  # 0保留给padding
        
        # 构建词汇表
        for smiles in smiles_list:
            for char in smiles:
                if char not in char_to_idx:
                    char_to_idx[char] = idx
                    idx += 1
        
        # 将SMILES转换为张量
        max_len = 100
        smiles_tensors = []
        
        for smiles in smiles_list:
            # 将SMILES字符串转换为索引序列
            indices = [char_to_idx.get(char, 0) for char in smiles]
            # 截断或填充到固定长度
            if len(indices) > max_len:
                indices = indices[:max_len]
            else:
                indices.extend([0] * (max_len - len(indices)))
            
            smiles_tensors.append(indices)
        
        smiles_tensors = torch.tensor(smiles_tensors, dtype=torch.long)
        
        # 生成模拟的3D点云数据
        batch_size = len(smiles_list)
        point_tensors = []
        
        for i in range(batch_size):
            # 距离矩阵 (50x50)
            distance_matrix = torch.randn(50, 50).abs()
            # 连接矩阵 (50x50) 
            connection_matrix = torch.randint(0, 2, (50, 50)).float()
            # 原子类型 (50,) - 确保在正确范围内
            atom_types = torch.randint(0, 79, (50,)).float()
            
            # 组合特征 [50, 101]
            point_features = torch.cat([
                distance_matrix,  # [50, 50]
                connection_matrix,  # [50, 50] 
                atom_types.unsqueeze(1)  # [50, 1]
            ], dim=1)
            
            point_tensors.append(point_features)
        
        point_tensors = torch.stack(point_tensors)
        
        # 处理标签
        median_value = np.median(labels)
        binary_labels = [1 if label > median_value else 0 for label in labels]
        processed_labels = torch.tensor(binary_labels, dtype=torch.long)
        
        print(f"预处理完成: {len(smiles_list)}个样本, 词汇表大小: {len(char_to_idx)}")
        
        return smiles_tensors, point_tensors, processed_labels
    
    def _create_dummy_data(self):
        """创建模拟数据用于测试"""
        print("使用模拟数据进行测试...")
        
        batch_size = 20
        smiles_list = ['CCO', 'CC(C)O', 'CCCO', 'CC(C)(C)O'] * (batch_size // 4 + 1)
        smiles_list = smiles_list[:batch_size]
        
        # 确保张量维度正确
        smiles_tensors = torch.randint(1, 65, (batch_size, 100))
        
        # 生成正确格式的3D点云数据
        point_tensors = []
        for i in range(batch_size):
            # 距离矩阵 (50x50)
            distance_matrix = torch.randn(50, 50).abs()
            # 连接矩阵 (50x50)
            connection_matrix = torch.randint(0, 2, (50, 50)).float()
            # 原子类型 (50,) - 确保在0-78范围内
            atom_types = torch.randint(0, 79, (50,)).float()
            
            # 组合特征 [50, 101]
            point_features = torch.cat([
                distance_matrix,  # [50, 50]
                connection_matrix,  # [50, 50]
                atom_types.unsqueeze(1)  # [50, 1]
            ], dim=1)
            
            point_tensors.append(point_features)
        
        point_tensors = torch.stack(point_tensors)
        labels = torch.randint(0, 2, (batch_size,))
        
        return {
            'smiles_list': smiles_list,
            'smiles_tensors': smiles_tensors,
            'point_tensors': point_tensors,
            'labels': labels
        }
    
    def run_channel_analysis(self):
        """运行通道重要性分析"""
        print("运行通道重要性分析...")
        
        # 创建数据加载器
        dataset = TensorDataset(
            self.data['smiles_tensors'][:20],
            self.data['point_tensors'][:20],
            self.data['labels'][:20]
        )
        data_loader = DataLoader(dataset, batch_size=4, shuffle=False)
        
        # 计算通道重要性
        importance_results = self.channel_analyzer.compute_channel_importance(data_loader)
        
        # 可视化结果
        self.channel_analyzer.visualize_channel_importance(
            importance_results, 
            save_path='results/channel_importance.png'
        )
        
        return importance_results
    
    def run_molecular_attention_analysis(self):
        """运行分子注意力可视化分析"""
        print("运行分子注意力可视化分析...")
        
        # 选择几个代表性样本进行分析
        sample_indices = [0, 5, 10, 15]
        
        for i, idx in enumerate(sample_indices):
            smiles = self.data['smiles_list'][idx]
            smiles_tensor = self.data['smiles_tensors'][idx]
            point_tensor = self.data['point_tensors'][idx]
            
            print(f"分析样本 {i+1}: {smiles}")
            
            # 提取注意力权重
            attention_data = self.attention_explainer.extract_attention_weights(
                smiles_tensor.unsqueeze(0),
                point_tensor.unsqueeze(0)
            )
            
            # 只生成分子注意力可视化
            self.attention_explainer.visualize_molecular_attention(
                smiles,
                attention_data['attention_weights'],
                save_path=f'results/molecular_attention_sample_{i+1}.png'
            )
        
        return {"molecular_attention_generated": True}

    def run_advanced_analysis(self):
        """运行高级可解释性分析"""
        print("运行高级可解释性分析...")

        # 选择一个代表性样本进行详细分析
        sample_idx = 0
        smiles_tensor = self.data['smiles_tensors'][sample_idx].unsqueeze(0)
        point_tensor = self.data['point_tensors'][sample_idx].unsqueeze(0)

        advanced_results = {}

        # LIME分析
        try:
            lime_result = self.advanced_explainer.lime_explanation(
                smiles_tensor, point_tensor, n_samples=500, n_features=10)
            advanced_results['LIME'] = lime_result
            print("LIME分析完成")
        except Exception as e:
            print(f"LIME分析失败: {e}")

        # SHAP分析
        try:
            shap_result = self.advanced_explainer.shap_explanation(
                smiles_tensor, point_tensor, n_samples=50)
            advanced_results['SHAP'] = shap_result
            print("SHAP分析完成")
        except Exception as e:
            print(f"SHAP分析失败: {e}")

        # 反事实分析
        try:
            counterfactual_result = self.advanced_explainer.counterfactual_explanation(
                smiles_tensor, point_tensor)
            advanced_results['Counterfactual'] = counterfactual_result
            print("反事实分析完成")
        except Exception as e:
            print(f"反事实分析失败: {e}")

        # 可视化高级分析结果
        if advanced_results:
            self.advanced_explainer.visualize_advanced_explanations(
                advanced_results, 'results/advanced_explanations.png')

        return advanced_results

    def run_comprehensive_channel_analysis(self):
        """运行综合通道分析（多种方法对比）"""
        print("运行综合通道分析...")

        # 创建数据加载器
        dataset = TensorDataset(
            self.data['smiles_tensors'][:10],  # 减少样本数量以加快速度
            self.data['point_tensors'][:10],
            self.data['labels'][:10]
        )
        data_loader = DataLoader(dataset, batch_size=2, shuffle=False)

        # 比较不同方法
        comparison_results = self.channel_analyzer.compare_methods(data_loader)

        # 可视化比较结果
        self.channel_analyzer.visualize_channel_importance(
            comparison_results,
            save_path='results/channel_importance_comparison.png'
        )

        return comparison_results

    def run_enhanced_molecular_attention(self):
        """运行增强的分子注意力分析"""
        print("运行增强的分子注意力分析...")

        # 选择几个代表性样本
        sample_indices = [0, 5, 10, 15]

        for i, idx in enumerate(sample_indices):
            if idx >= len(self.data['smiles_list']):
                continue

            smiles = self.data['smiles_list'][idx]
            smiles_tensor = self.data['smiles_tensors'][idx].unsqueeze(0)
            point_tensor = self.data['point_tensors'][idx].unsqueeze(0)

            print(f"分析样本 {i+1}: {smiles}")

            # 使用不同方法提取注意力权重
            methods = ['gradient', 'integrated_gradients']
            attention_results = {}

            for method in methods:
                try:
                    attention_data = self.attention_explainer.extract_attention_weights(
                        smiles_tensor, point_tensor, method=method)
                    attention_results[method] = attention_data
                except Exception as e:
                    print(f"方法 {method} 失败: {e}")

            # 生成综合可视化
            if attention_results:
                self.attention_explainer.visualize_molecular_attention(
                    smiles, attention_results,
                    save_path=f'results/enhanced_molecular_attention_sample_{i+1}.png',
                    visualization_type='comprehensive'
                )

        return {"enhanced_molecular_attention_generated": True}

    def run_complete_analysis(self):
        """运行完整的可解释性分析 - 包含所有增强功能"""
        print("开始全面可解释性分析...")

        # 检查模型是否加载成功
        if self.model is None:
            print("错误: 模型未加载成功，无法进行分析")
            return None

        if (self.channel_analyzer is None or self.attention_explainer is None or
            self.advanced_explainer is None):
            print("错误: 分析器未初始化，无法进行分析")
            return None

        results = {}

        # 1. 基础通道重要性分析
        print("\n1. 基础通道重要性分析")
        channel_results = self.run_channel_analysis()
        results['basic_channel_analysis'] = channel_results

        # 2. 综合通道重要性分析（多方法对比）
        print("\n2. 综合通道重要性分析")
        comprehensive_channel_results = self.run_comprehensive_channel_analysis()
        results['comprehensive_channel_analysis'] = comprehensive_channel_results

        # 3. 基础分子结构注意力可视化
        print("\n3. 基础分子结构注意力可视化")
        attention_results = self.run_molecular_attention_analysis()
        results['basic_molecular_attention'] = attention_results

        # 4. 增强分子结构注意力可视化
        print("\n4. 增强分子结构注意力可视化")
        enhanced_attention_results = self.run_enhanced_molecular_attention()
        results['enhanced_molecular_attention'] = enhanced_attention_results

        # 5. 高级可解释性分析（LIME, SHAP, 反事实）
        print("\n5. 高级可解释性分析")
        advanced_results = self.run_advanced_analysis()
        results['advanced_analysis'] = advanced_results

        # 生成综合报告
        self._generate_comprehensive_report(results)

        print("\n🎉 全面可解释性分析完成！")
        print("\n📁 生成的文件:")
        print("基础分析:")
        print("- channel_importance.png (基础通道重要性)")
        print("- molecular_attention_sample_*.png (基础分子注意力)")
        print("\n增强分析:")
        print("- channel_importance_comparison.png (多方法通道对比)")
        print("- enhanced_molecular_attention_sample_*.png (增强分子注意力)")
        print("- advanced_explanations.png (LIME/SHAP/反事实分析)")
        print("\n报告:")
        print("- comprehensive_interpretability_report.txt (综合分析报告)")

        print("\n🔬 分析方法总结:")
        print("通道重要性: 梯度归因法、集成梯度法、特征消融法")
        print("分子注意力: 梯度方法、集成梯度方法、引导反向传播")
        print("高级解释: LIME局部解释、SHAP博弈论解释、反事实分析")

        return results
    
    def _generate_report(self, results):
        """生成可解释性分析报告"""
        report_path = 'results/interpretability_report.txt'

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("=== 鱼类LC50毒性预测模型可解释性分析报告 ===\n\n")

            # 通道重要性分析
            if 'channel_analysis' in results:
                channel_data = results['channel_analysis']
                f.write("1. 通道重要性分析 (梯度归因法)\n")
                f.write(f"SMILES通道重要性: {channel_data['smiles_importance']:.4f}\n")
                f.write(f"3D结构通道重要性: {channel_data['point_importance']:.4f}\n")

                total_importance = channel_data['smiles_importance'] + channel_data['point_importance']
                if total_importance > 0:
                    smiles_contrib = channel_data['smiles_importance'] / total_importance * 100
                    point_contrib = channel_data['point_importance'] / total_importance * 100
                    f.write(f"SMILES通道贡献: {smiles_contrib:.1f}%\n")
                    f.write(f"3D结构通道贡献: {point_contrib:.1f}%\n")
                f.write("说明: 数值越大表示该通道对模型预测的影响越大\n")
                f.write("\n")

            # 分子结构注意力分析结果
            if 'molecular_attention' in results:
                f.write("2. 分子结构注意力可视化\n")
                f.write("已生成4个样本的简洁分子结构注意力可视化图像\n")
                f.write("文件: molecular_attention_sample_1.png ~ molecular_attention_sample_4.png\n")
                f.write("可视化特点:\n")
                f.write("- 简洁的分子结构图显示\n")
                f.write("- 标准原子颜色 (C=黑色, N=蓝色, O=红色等)\n")
                f.write("- 高注意力原子用绿色圆圈标记\n")
                f.write("- 黑色键线连接原子\n")
                f.write("- 类似标准化学结构图样式\n")
                f.write("\n")

            # 分析方法说明
            f.write("3. 分析方法说明\n")
            f.write("- 通道重要性: 使用梯度归因法计算SMILES和3D结构通道的相对贡献\n")
            f.write("- 分子结构注意力: 基于模型梯度计算的注意力权重，识别重要原子\n")
            f.write("- 可视化方式: 简洁的标准化学结构图，绿色圆圈标记高注意力原子\n")
            f.write("- 设计理念: 类似标准化学教科书中的分子结构图样式\n")
            f.write("\n")

            f.write("分析完成时间: " + str(pd.Timestamp.now()) + "\n")

    def _generate_comprehensive_report(self, results):
        """生成综合可解释性分析报告"""
        report_path = 'results/comprehensive_interpretability_report.txt'

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("=== 鱼类LC50毒性预测模型 - 综合可解释性分析报告 ===\n\n")

            # 1. 基础通道重要性分析
            if 'basic_channel_analysis' in results:
                basic_channel = results['basic_channel_analysis']
                f.write("1. 基础通道重要性分析\n")
                f.write(f"SMILES通道重要性: {basic_channel['smiles_importance']:.4f}\n")
                f.write(f"3D结构通道重要性: {basic_channel['point_importance']:.4f}\n")

                total = basic_channel['smiles_importance'] + basic_channel['point_importance']
                if total > 0:
                    f.write(f"SMILES通道贡献: {basic_channel['smiles_importance']/total*100:.1f}%\n")
                    f.write(f"3D结构通道贡献: {basic_channel['point_importance']/total*100:.1f}%\n")
                f.write("\n")

            # 2. 综合通道重要性分析
            if 'comprehensive_channel_analysis' in results:
                comp_channel = results['comprehensive_channel_analysis']
                f.write("2. 综合通道重要性分析 (多方法对比)\n")

                for method, result in comp_channel.items():
                    method_name = {
                        'gradient_attribution': '梯度归因法',
                        'integrated_gradients': '集成梯度法',
                        'feature_ablation': '特征消融法'
                    }.get(method, method)

                    f.write(f"\n{method_name}:\n")
                    f.write(f"  SMILES通道: {result['smiles_importance']:.4f}\n")
                    f.write(f"  3D结构通道: {result['point_importance']:.4f}\n")

                    total = result['smiles_importance'] + result['point_importance']
                    if total > 0:
                        f.write(f"  SMILES贡献: {result['smiles_importance']/total*100:.1f}%\n")
                        f.write(f"  3D结构贡献: {result['point_importance']/total*100:.1f}%\n")
                f.write("\n")

            # 3. 分子结构注意力分析
            f.write("3. 分子结构注意力分析\n")
            if 'basic_molecular_attention' in results:
                f.write("基础分子注意力可视化: 已生成4个样本的简洁可视化\n")
            if 'enhanced_molecular_attention' in results:
                f.write("增强分子注意力可视化: 已生成多方法对比的综合可视化\n")
            f.write("特点: 标准CPK颜色、注意力热力图、权重分布图\n\n")

            # 4. 高级可解释性分析
            if 'advanced_analysis' in results:
                advanced = results['advanced_analysis']
                f.write("4. 高级可解释性分析\n")

                if 'LIME' in advanced:
                    lime_result = advanced['LIME']
                    f.write(f"LIME分析:\n")
                    f.write(f"  解释质量 (R²): {lime_result['explanation_score']:.4f}\n")
                    f.write(f"  目标类别: {lime_result['target_class']}\n")
                    f.write(f"  原始预测: {lime_result['original_prediction']:.4f}\n")

                if 'SHAP' in advanced:
                    shap_result = advanced['SHAP']
                    f.write(f"SHAP分析:\n")
                    f.write(f"  目标类别: {shap_result['target_class']}\n")
                    f.write(f"  原始预测: {shap_result['original_prediction']:.4f}\n")
                    f.write(f"  SMILES平均SHAP: {np.mean(shap_result['smiles_shap_values']):.4f}\n")
                    f.write(f"  3D结构平均SHAP: {np.mean(shap_result['point_shap_values']):.4f}\n")

                if 'Counterfactual' in advanced:
                    cf_result = advanced['Counterfactual']
                    f.write(f"反事实分析:\n")
                    f.write(f"  反事实生成: {'成功' if cf_result['counterfactual_found'] else '失败'}\n")
                    f.write(f"  原始类别: {cf_result['original_class']}\n")
                    f.write(f"  目标类别: {cf_result['target_class']}\n")
                    f.write(f"  反事实置信度: {cf_result['counterfactual_confidence']:.4f}\n")
                f.write("\n")

            # 5. 方法说明
            f.write("5. 分析方法详细说明\n")
            f.write("通道重要性分析:\n")
            f.write("  • 梯度归因法: 计算特征×梯度的绝对值\n")
            f.write("  • 集成梯度法: 从基线到输入的路径积分\n")
            f.write("  • 特征消融法: 移除特征后的预测变化\n\n")

            f.write("分子注意力分析:\n")
            f.write("  • 梯度方法: 基于反向传播的梯度\n")
            f.write("  • 集成梯度: 路径积分的注意力权重\n")
            f.write("  • 引导反向传播: 只保留正梯度的方法\n\n")

            f.write("高级解释方法:\n")
            f.write("  • LIME: 局部线性近似解释\n")
            f.write("  • SHAP: 基于博弈论的公平分配\n")
            f.write("  • 反事实: 寻找最小变化使预测翻转\n\n")

            # 6. 结论和建议
            f.write("6. 结论和建议\n")
            f.write("本分析提供了多角度的模型可解释性视图:\n")
            f.write("• 通道重要性帮助理解SMILES和3D结构的相对贡献\n")
            f.write("• 分子注意力识别与毒性相关的关键原子和结构\n")
            f.write("• 高级解释方法提供了更深入的模型行为洞察\n")
            f.write("• 多方法对比增强了分析结果的可靠性\n\n")

            f.write("建议:\n")
            f.write("• 结合多种方法的结果进行综合判断\n")
            f.write("• 关注在不同方法中一致性高的特征\n")
            f.write("• 利用可视化结果进行直观的模型理解\n")
            f.write("• 将解释结果用于模型改进和特征工程\n\n")

            f.write("分析完成时间: " + str(pd.Timestamp.now()) + "\n")

def main():
    # 配置参数
    model_path = 'models/FishLC50_predictor.pth'  # 模型路径
    data_path = 'input_data_FishLC50.xlsx'  # 数据路径
    device = 'cpu'  # 强制使用CPU避免CUDA问题
    
    print(f"使用设备: {device}")
    
    # 创建分析实例
    analysis = SimplifiedInterpretabilityAnalysis(model_path, data_path, device)
    
    # 运行完整分析
    results = analysis.run_complete_analysis()
    
    return results

if __name__ == "__main__":
    main()