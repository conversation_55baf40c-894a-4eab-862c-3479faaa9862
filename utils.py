import numpy as np
import pandas as pd
from sklearn.metrics import roc_auc_score, average_precision_score, confusion_matrix, roc_curve
from rdkit import Chem
from rdkit.Chem import Descriptors, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, rdMolDescriptors
import torch

# 参数设置
drug_max_length = 100
max_atoms = 50

# 原子字典
atom_dict = {"#": 29, "%": 30, ")": 31, "(": 1, "+": 32, "-": 33, "/": 34, ".": 2, "1": 35, "0": 3,
            "3": 36, "2": 4, "5": 37, "4": 5, "7": 38, "6": 6, "9": 39, "8": 7, "=": 40, "A": 41,
            "@": 8, "C": 42, "B": 9, "E": 43, "D": 10, "G": 44, "F": 11, "I": 45, "H": 12, "K": 46,
            "M": 47, "L": 13, "O": 48, "N": 14, "P": 15, "S": 49, "R": 16, "U": 50, "T": 17, "W": 51,
            "V": 18, "Y": 52, "[": 53, "Z": 19, "]": 54, "\\": 20, "a": 55, "c": 56, "b": 21, "e": 57,
            "d": 22, "g": 58, "f": 23, "i": 59, "h": 24, "m": 60, "l": 25, "o": 61, "n": 26, "s": 62,
            "r": 27, "u": 63, "t": 28, "y": 64}

def get_atom_features(atom):
    """提取丰富的原子特征"""
    features = []
    
    # 基本原子属性
    features.append(atom.GetAtomicNum())  # 原子序数
    features.append(atom.GetDegree())     # 度数
    features.append(atom.GetFormalCharge())  # 正式电荷
    features.append(int(atom.GetHybridization()))  # 杂化类型
    features.append(atom.GetNumRadicalElectrons())  # 自由基电子数
    
    # 芳香性和环信息
    features.append(int(atom.GetIsAromatic()))  # 是否芳香
    features.append(int(atom.IsInRing()))       # 是否在环中
    
    # 氢原子信息
    features.append(atom.GetTotalNumHs())       # 总氢原子数
    features.append(atom.GetNumImplicitHs())    # 隐式氢原子数
    
    # 化学环境
    features.append(int(atom.GetChiralTag()))   # 手性标签
    
    return features


def get_bond_features(bond):
    """提取化学键特征"""
    features = []
    
    # 键类型
    bond_type = bond.GetBondType()
    features.append(int(bond_type == Chem.rdchem.BondType.SINGLE))
    features.append(int(bond_type == Chem.rdchem.BondType.DOUBLE))
    features.append(int(bond_type == Chem.rdchem.BondType.TRIPLE))
    features.append(int(bond_type == Chem.rdchem.BondType.AROMATIC))
    
    # 键属性
    features.append(int(bond.GetIsConjugated()))  # 是否共轭
    features.append(int(bond.IsInRing()))         # 是否在环中
    features.append(int(bond.GetStereo()))        # 立体化学信息
    
    return features


def get_molecular_descriptors(mol):
    """计算分子级别的拓扑描述符"""
    descriptors = []
    
    try:
        # 基本分子属性
        descriptors.append(Descriptors.MolWt(mol))           # 分子量
        descriptors.append(Descriptors.NumHDonors(mol))      # 氢键供体数
        descriptors.append(Descriptors.NumHAcceptors(mol))   # 氢键受体数
        descriptors.append(Descriptors.NumRotatableBonds(mol))  # 可旋转键数
          # 拓扑指数
        descriptors.append(Descriptors.BalabanJ(mol))        # Balaban指数
        descriptors.append(Descriptors.BertzCT(mol))         # Bertz复杂度
        descriptors.append(Descriptors.Kappa1(mol))          # Kappa1指数
        
        # 环系统信息
        descriptors.append(Descriptors.NumAliphaticRings(mol))  # 脂肪环数
        descriptors.append(Descriptors.NumAromaticRings(mol))   # 芳香环数
        descriptors.append(Descriptors.NumSaturatedRings(mol))  # 饱和环数
        
        # 物理化学性质
        descriptors.append(Crippen.MolLogP(mol))             # LogP
        descriptors.append(Descriptors.TPSA(mol))            # 拓扑极性表面积
        
    except Exception as e:
        # 如果计算失败，返回默认值
        descriptors = [0.0] * 12
    
    return descriptors


def smiles_to_sequence(smiles, max_length=drug_max_length):
    """将SMILES转换为序列"""
    sequence = np.zeros(max_length, dtype=np.int64)
    for i, char in enumerate(smiles[:max_length]):
        if char in atom_dict:
            sequence[i] = atom_dict[char]
        else:
            sequence[i] = 0
    return sequence


def smiles_to_graph_enhanced(smiles, max_atoms=max_atoms):
    """增强版分子图编码函数"""
    try:
        mol = Chem.MolFromSmiles(smiles)
        if mol is None:
            return create_empty_graph_data(max_atoms)
        
        mol = Chem.AddHs(mol)  # 添加氢原子
        num_atoms = mol.GetNumAtoms()
        
        if num_atoms > max_atoms:
            return create_empty_graph_data(max_atoms)
        
        # 1. 增强的原子特征矩阵 (10维特征)
        atom_features = np.zeros((max_atoms, 10))
        for i, atom in enumerate(mol.GetAtoms()):
            if i < max_atoms:
                features = get_atom_features(atom)
                atom_features[i, :len(features)] = features
        
        # 2. 增强的邻接矩阵（包含键特征）(7维特征)
        bond_features = np.zeros((max_atoms, max_atoms, 7))
        for bond in mol.GetBonds():
            i, j = bond.GetBeginAtomIdx(), bond.GetEndAtomIdx()
            if i < max_atoms and j < max_atoms:
                features = get_bond_features(bond)
                bond_features[i, j, :len(features)] = features
                bond_features[j, i, :len(features)] = features  # 对称
        
        # 3. 拓扑距离矩阵（多跳邻居信息）
        distance_matrix = np.zeros((max_atoms, max_atoms))
        
        # 计算最短路径距离
        for i in range(num_atoms):
            for j in range(num_atoms):
                if i != j and i < max_atoms and j < max_atoms:
                    try:
                        path = Chem.GetShortestPath(mol, i, j)
                        if path:
                            distance_matrix[i][j] = len(path) - 1
                        else:
                            distance_matrix[i][j] = max_atoms  # 断开的片段
                    except:
                        distance_matrix[i][j] = max_atoms
        
        # 4. 环信息编码
        ring_info = np.zeros((max_atoms,))
        ring_systems = mol.GetRingInfo()
        for ring in ring_systems.AtomRings():
            for atom_idx in ring:
                if atom_idx < max_atoms:
                    ring_info[atom_idx] = len(ring)  # 环大小
        
        # 5. 分子级描述符
        mol_descriptors = np.array(get_molecular_descriptors(mol))
        
        # 组合所有特征为一个字典
        graph_data = {
            'atom_features': atom_features,      # [max_atoms, 10]
            'bond_features': bond_features,      # [max_atoms, max_atoms, 7]
            'distance_matrix': distance_matrix,  # [max_atoms, max_atoms]
            'ring_info': ring_info,              # [max_atoms]
            'mol_descriptors': mol_descriptors   # [12]
        }
        
        return graph_data
        
    except Exception as e:
        print(f"处理SMILES {smiles} 时出错: {e}")
        return create_empty_graph_data(max_atoms)


def create_empty_graph_data(max_atoms):
    """创建空的图数据"""
    return {
        'atom_features': np.zeros((max_atoms, 10)),
        'bond_features': np.zeros((max_atoms, max_atoms, 7)),
        'distance_matrix': np.zeros((max_atoms, max_atoms)),
        'ring_info': np.zeros((max_atoms,)),
        'mol_descriptors': np.zeros(12)
    }


def smiles_to_graph(smiles, max_atoms=max_atoms):
    """将SMILES转换为图表示"""
    try:
        mol = Chem.MolFromSmiles(smiles)
        if mol is None:
            return np.zeros((max_atoms, max_atoms * 2 + 1))
        
        # 获取原子数量
        num_atoms = mol.GetNumAtoms()
        if num_atoms == 0:
            return np.zeros((max_atoms, max_atoms * 2 + 1))
        
        # 限制原子数量
        actual_atoms = min(num_atoms, max_atoms)
        
        # 初始化矩阵
        distance_matrix = np.zeros((max_atoms, max_atoms))
        connect_matrix = np.zeros((max_atoms, max_atoms))
        atom_features = np.zeros(max_atoms)
        
        # 连接矩阵（化学键）
        for bond in mol.GetBonds():
            i, j = bond.GetBeginAtomIdx(), bond.GetEndAtomIdx()
            if i < max_atoms and j < max_atoms:
                connect_matrix[i][j] = connect_matrix[j][i] = 1
        
        # 距离矩阵（简化版本，使用拓扑距离）
        for i in range(actual_atoms):
            for j in range(actual_atoms):
                if i != j:
                    # 使用最短路径作为距离的近似
                    try:
                        path = Chem.GetShortestPath(mol, i, j)
                        if path:
                            distance_matrix[i][j] = 1.0 / len(path)
                    except:
                        distance_matrix[i][j] = 0
        
        # 原子特征（原子序数，限制在合理范围内）
        for i in range(actual_atoms):
            atom = mol.GetAtomWithIdx(i)
            atom_num = atom.GetAtomicNum()
            # 限制原子序数在0-99范围内，避免嵌入层越界
            atom_features[i] = min(atom_num, 99)
        
        # 组合所有特征
        graph_data = np.hstack([
            distance_matrix,
            connect_matrix,
            atom_features.reshape(-1, 1)
        ])
        
        return graph_data
        
    except Exception as e:
        return np.zeros((max_atoms, max_atoms * 2 + 1))


def prepare_data(df):
    """准备训练数据"""
    smiles_sequences = []
    graph_data = []
    labels = []
    
    for idx, row in df.iterrows():
        smiles = row['smiles']
        label = row['FishLC50'] 
        
        # SMILES序列编码
        seq = smiles_to_sequence(smiles)
        smiles_sequences.append(seq)
        
        # 图数据编码
        graph = smiles_to_graph(smiles)
        graph_data.append(graph)
        
        labels.append(label)
    
    # 转换为numpy数组再转换为tensor，提高效率
    smiles_array = np.array(smiles_sequences, dtype=np.int64)
    graph_array = np.array(graph_data, dtype=np.float32)
    labels_array = np.array(labels, dtype=np.int64)
    
    smiles_tensor = torch.from_numpy(smiles_array)
    graph_tensor = torch.from_numpy(graph_array)
    label_tensor = torch.from_numpy(labels_array)
    
    return smiles_tensor, graph_tensor, label_tensor


def evaluate_model(y_pred, y_true):
    """评估模型性能"""
    y_pred = np.array(y_pred)
    y_true = np.array(y_true)
    
    # 计算AUC
    auc_score = roc_auc_score(y_true, y_pred)
    
    # 计算AUPR
    aupr_score = average_precision_score(y_true, y_pred)
    
    # 找到最优阈值
    fpr, tpr, thresholds = roc_curve(y_true, y_pred)
    precision = tpr / (tpr + fpr + 1e-8)
    f1 = 2 * precision * tpr / (tpr + precision + 1e-8)
    optimal_threshold = thresholds[np.argmax(f1)]
    
    # 计算平衡准确率、敏感性、特异性
    y_pred_binary = (y_pred >= optimal_threshold).astype(int)
    cm = confusion_matrix(y_true, y_pred_binary)
    
    # 平衡准确率 = (敏感性 + 特异性) / 2
    sensitivity = cm[1, 1] / (cm[1, 0] + cm[1, 1]) if (cm[1, 0] + cm[1, 1]) > 0 else 0
    specificity = cm[0, 0] / (cm[0, 0] + cm[0, 1]) if (cm[0, 0] + cm[0, 1]) > 0 else 0
    balanced_accuracy = (sensitivity + specificity) / 2
    
    return auc_score, aupr_score, balanced_accuracy, sensitivity, specificity


def seed_torch(seed_value):
    """设置随机种子"""
    import random
    import os
    
    random.seed(seed_value)
    os.environ['PYTHONHASHSEED'] = str(seed_value)
    np.random.seed(seed_value)
    torch.manual_seed(seed_value)
    torch.cuda.manual_seed(seed_value)
    torch.cuda.manual_seed_all(seed_value)
    torch.backends.cudnn.benchmark = False
    torch.backends.cudnn.deterministic = True