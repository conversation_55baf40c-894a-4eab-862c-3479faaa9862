import torch
import torch.nn as nn
import math

# 参数设置
feature_size = 128
smiles_kernel_num = 32
smiles_kernel_size = 8
smiles_input_size = 128
point_hidden_size = 128
point_kernel_num = 32
point_kernel_size = 8
atom_dic_length = 65
atom_point_dict_length = 79


class DrugSMILESCoding(nn.Module):
    """SMILES序列编码模块 - 添加正则化"""
    def __init__(self, hid_dim=smiles_input_size, out_dim=feature_size, vocab_size=atom_dic_length,
                 channel=smiles_kernel_num, kernel_size=smiles_kernel_size, dropout_rate=0.3):
        super(DrugSMILESCoding, self).__init__()
        self.embedding = nn.Embedding(vocab_size, embedding_dim=hid_dim)
        self.embedding_dropout = nn.Dropout(dropout_rate * 0.5)  # 嵌入层轻度dropout
        
        # 卷积层 + BatchNorm + Dropout
        self.conv1 = nn.Conv1d(hid_dim, channel, kernel_size, padding=kernel_size-1)
        self.bn1 = nn.BatchNorm1d(channel)
        self.dropout1 = nn.Dropout(dropout_rate * 0.6)
        
        self.conv2 = nn.Conv1d(channel, channel*2, kernel_size, padding=kernel_size-1)
        self.bn2 = nn.BatchNorm1d(channel*2)
        self.dropout2 = nn.Dropout(dropout_rate * 0.7)
        
        self.conv3 = nn.Conv1d(channel*2, channel*4, kernel_size, padding=kernel_size-1)
        self.bn3 = nn.BatchNorm1d(channel*4)
        self.dropout3 = nn.Dropout(dropout_rate)
        
        self.act = nn.LeakyReLU(0.2)
        self.globalmaxpool = nn.AdaptiveMaxPool1d(1)
        
        # 全连接层正则化
        self.fc1 = nn.Linear(channel*4, out_dim)
        self.fc_dropout = nn.Dropout(dropout_rate)

    def forward(self, x):
        # 嵌入 + 轻度dropout
        x = self.embedding(x)
        x = self.embedding_dropout(x)
        x = x.permute(0, 2, 1)
        
        # 第一层卷积 + BN + Dropout
        x = self.conv1(x)
        x = self.bn1(x)
        x = self.act(x)
        x = self.dropout1(x)
        
        # 第二层卷积 + BN + Dropout
        x = self.conv2(x)
        x = self.bn2(x)
        x = self.act(x)
        x = self.dropout2(x)
        
        # 第三层卷积 + BN + Dropout
        x = self.conv3(x)
        x = self.bn3(x)
        x = self.act(x)
        x = self.dropout3(x)
        
        # 全局池化 + 全连接
        x = self.globalmaxpool(x)
        x = x.squeeze(-1)
        x = self.fc_dropout(x)
        x = self.fc1(x)
        return x


class DrugPointCoding(nn.Module):
    """3D点云编码模块 - 添加正则化"""
    def __init__(self, point_hid_dim=point_hidden_size, point_output_dim=feature_size,
                 channel=point_kernel_num, kernel_size=point_kernel_size, dropout_rate=0.3):
        super(DrugPointCoding, self).__init__()
        
        # GCN层 + 正则化
        self.atom_embedding = nn.Embedding(atom_point_dict_length, point_hid_dim)
        self.gcn1_linear = nn.Linear(point_hid_dim, point_hid_dim)
        self.gcn1_bn = nn.BatchNorm1d(point_hid_dim)
        self.gcn1_dropout = nn.Dropout(dropout_rate * 0.5)
        
        self.gcn2 = nn.Linear(point_hid_dim, point_output_dim)
        self.gcn2_bn = nn.BatchNorm1d(point_output_dim)
        self.gcn2_dropout = nn.Dropout(dropout_rate * 0.6)
        
        # 图卷积融合层
        self.conv1 = nn.Conv2d(in_channels=2, out_channels=1, kernel_size=1)
        self.conv2 = nn.Conv2d(in_channels=2, out_channels=1, kernel_size=1)
        
        # 1D卷积层 + 正则化
        self.conv_layer1 = nn.Conv1d(point_output_dim, channel, kernel_size, padding=kernel_size - 1)
        self.conv_bn1 = nn.BatchNorm1d(channel)
        self.conv_dropout1 = nn.Dropout(dropout_rate * 0.6)
        
        self.conv_layer2 = nn.Conv1d(channel, channel * 2, kernel_size, padding=kernel_size - 1)
        self.conv_bn2 = nn.BatchNorm1d(channel * 2)
        self.conv_dropout2 = nn.Dropout(dropout_rate * 0.7)
        
        self.conv_layer3 = nn.Conv1d(channel * 2, channel * 4, kernel_size, padding=kernel_size - 1)
        self.conv_bn3 = nn.BatchNorm1d(channel * 4)
        self.conv_dropout3 = nn.Dropout(dropout_rate)
        
        self.act = nn.LeakyReLU(0.2)
        self.globalmaxpool = nn.AdaptiveMaxPool1d(1)
        
        # 最终全连接层
        self.fc1 = nn.Linear(channel * 4, point_output_dim)
        self.fc_dropout = nn.Dropout(dropout_rate)

    def forward(self, x):
        # x: [batch_size, max_atoms, max_atoms*2 + 1]
        # 距离矩阵、连接矩阵、原子特征
        batch_size, max_atoms = x.shape[0], x.shape[1]
        
        d_distance = x[:, :, :max_atoms]  # 3D空间距离矩阵
        d_connect = x[:, :, max_atoms:max_atoms*2]  # 化学键连接矩阵
        d_atoms = x[:, :, -1].long()  # 原子特征
        
        # 原子特征嵌入和第一层GCN + 正则化
        h1 = self.atom_embedding(d_atoms)
        h1 = self.gcn1_linear(h1)
        
        # BatchNorm需要调整维度
        b, n, f = h1.shape
        h1 = h1.view(b * n, f)
        h1 = self.gcn1_bn(h1)
        h1 = h1.view(b, n, f)
        h1 = self.act(h1)
        h1 = self.gcn1_dropout(h1)
        
        # 双通道图卷积
        h1_1 = torch.einsum('ijk, ikp->ijp', d_distance, h1)
        h1_2 = torch.einsum('ijk, ikp->ijp', d_connect, h1)

        b, r, c = h1_1.shape
        h1_1 = self.act(h1_1).view(b, 1, r, c)
        h1_2 = self.act(h1_2).view(b, 1, r, c)
        h1_12 = torch.cat((h1_1, h1_2), dim=1)
        h1_c = self.conv1(h1_12).view(b, r, c)
        h1 = self.act(h1_c)

        # 第二层GCN + 正则化
        h2 = self.gcn2(h1)
        
        # BatchNorm
        b, n, f = h2.shape
        h2 = h2.view(b * n, f)
        h2 = self.gcn2_bn(h2)
        h2 = h2.view(b, n, f)
        h2 = self.act(h2)
        h2 = self.gcn2_dropout(h2)

        h2_1 = torch.einsum('ijk, ikp->ijp', d_distance, h2)
        h2_2 = torch.einsum('ijk, ikp->ijp', d_connect, h2)
        b, r, c = h2_1.shape
        h2_1 = self.act(h2_1).view(b, 1, r, c)
        h2_2 = self.act(h2_2).view(b, 1, r, c)
        h2_12 = torch.cat((h2_1, h2_2), dim=1)
        h2_c = self.conv2(h2_12).view(b, r, c)
        h2 = self.act(h2_c)
        
        # 1D卷积和池化 + 正则化
        x = h2.permute(0, 2, 1)  # [batch_size, point_output_dim, max_atoms]
        
        # 第一层1D卷积
        x = self.conv_layer1(x)
        x = self.conv_bn1(x)
        x = self.act(x)
        x = self.conv_dropout1(x)
        
        # 第二层1D卷积
        x = self.conv_layer2(x)
        x = self.conv_bn2(x)
        x = self.act(x)
        x = self.conv_dropout2(x)
        
        # 第三层1D卷积
        x = self.conv_layer3(x)
        x = self.conv_bn3(x)
        x = self.act(x)
        x = self.conv_dropout3(x)
        
        # 全局池化和最终输出
        x = self.globalmaxpool(x)
        x = x.squeeze(-1)
        x = self.fc_dropout(x)
        x = self.fc1(x)
        return x


class FishLC50Predictor(nn.Module):
    """预测模型 - 双通道架构 + 强化正则化"""
    def __init__(self, dropout_rate=0.4):
        super(FishLC50Predictor, self).__init__()
        # 两个通道编码器，传入dropout参数
        self.smiles_encoder = DrugSMILESCoding(dropout_rate=dropout_rate * 0.75)
        self.point_encoder = DrugPointCoding(dropout_rate=dropout_rate * 0.75)
        
        # 特征融合层 + 正则化
        self.feature_bn = nn.BatchNorm1d(feature_size * 2)
        self.feature_dropout = nn.Dropout(dropout_rate * 0.5)
        
        # 预测层 + 渐进式dropout
        self.fc1 = nn.Linear(feature_size * 2, 512)
        self.fc1_bn = nn.BatchNorm1d(512)
        self.fc1_dropout = nn.Dropout(dropout_rate * 0.6)
        
        self.fc2 = nn.Linear(512, 256)
        self.fc2_bn = nn.BatchNorm1d(256)
        self.fc2_dropout = nn.Dropout(dropout_rate * 0.8)
        
        self.fc3 = nn.Linear(256, 128)
        self.fc3_bn = nn.BatchNorm1d(128)
        self.fc3_dropout = nn.Dropout(dropout_rate)
        
        self.fc4 = nn.Linear(128, 2)  # 最终输出层
        
        self.act = nn.LeakyReLU(0.2)
        
        # 权重初始化
        self._init_weights()

    def _init_weights(self):
        """Xavier初始化权重"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Conv1d):
                nn.init.kaiming_uniform_(m.weight, mode='fan_in', nonlinearity='leaky_relu')
            elif isinstance(m, nn.BatchNorm1d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)

    def forward(self, smiles_data, point_data):
        # SMILES通道特征提取 (内含正则化)
        smiles_features = self.smiles_encoder(smiles_data)
        
        # 3D点云通道特征提取 (内含正则化)
        point_features = self.point_encoder(point_data)
        
        # 特征融合 + 正则化
        combined = torch.cat((smiles_features, point_features), dim=1)
        combined = self.feature_bn(combined)
        combined = self.feature_dropout(combined)
        
        # 第一层预测
        x = self.fc1(combined)
        x = self.fc1_bn(x)
        x = self.act(x)
        x = self.fc1_dropout(x)
        
        # 第二层预测
        x = self.fc2(x)
        x = self.fc2_bn(x)
        x = self.act(x)
        x = self.fc2_dropout(x)
        
        # 第三层预测
        x = self.fc3(x)
        x = self.fc3_bn(x)
        x = self.act(x)
        x = self.fc3_dropout(x)
        
        # 最终输出层 (无正则化，保持输出稳定)
        x = self.fc4(x)
        return x