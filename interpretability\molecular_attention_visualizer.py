import torch
import torch.nn.functional as F
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import re

class MolecularAttentionVisualizer:
    """
    分子结构注意力可视化器
    提取和可视化分子结构的注意力权重
    """
    
    def __init__(self, model, device='cpu'):
        """
        初始化分子注意力可视化器
        
        Args:
            model: 训练好的模型
            device: 计算设备
        """
        self.model = model
        self.device = device
        self.model.to(device)
        self.model.eval()
        
        # 标准原子颜色映射（简化版本）
        self.atom_colors = {
            'C': '#000000',  # 黑色 - 碳
            'N': '#0000FF',  # 蓝色 - 氮
            'O': '#FF0000',  # 红色 - 氧
            'S': '#FFFF00',  # 黄色 - 硫
            'P': '#FFA500',  # 橙色 - 磷
            'F': '#00FF00',  # 绿色 - 氟
            'Cl': '#00FF00', # 绿色 - 氯
            'Br': '#A52A2A', # 棕色 - 溴
            'I': '#9400D3',  # 紫色 - 碘
            'H': '#FFFFFF',  # 白色 - 氢
            'default': '#808080'  # 灰色 - 其他
        }
    
    def extract_attention_weights(self, smiles_tensor, point_tensor):
        """
        提取注意力权重
        
        Args:
            smiles_tensor: SMILES张量
            point_tensor: 3D点云张量
            
        Returns:
            dict: 包含注意力权重的字典
        """
        # 移动数据到设备
        smiles_tensor = smiles_tensor.to(self.device).long()  # SMILES需要long类型用于embedding
        point_tensor = point_tensor.to(self.device).float()

        # 只对point_tensor启用梯度计算（因为它是float类型）
        point_tensor.requires_grad_(True)
        
        # 前向传播
        outputs = self.model(smiles_tensor, point_tensor)
        probs = F.softmax(outputs, dim=1)

        # 计算对最大概率类别的梯度
        max_class = torch.argmax(probs, dim=1)
        target_prob = probs[0, max_class[0]]

        # 反向传播获取梯度
        target_prob.backward()

        # 提取point_tensor的梯度作为注意力权重
        point_grad = point_tensor.grad.abs().cpu().numpy()[0]    # [num_points, features]

        # 对点云特征求和得到每个原子的注意力
        atom_attention = np.sum(point_grad, axis=1)  # [num_points]

        # 归一化注意力权重
        if np.sum(atom_attention) > 0:
            atom_attention = atom_attention / np.sum(atom_attention)

        # 对于SMILES，我们使用一个简化的方法：基于序列长度生成均匀权重
        smiles_length = smiles_tensor.size(1)
        smiles_attention = np.ones(smiles_length) / smiles_length

        return {
            'attention_weights': atom_attention,
            'smiles_attention': smiles_attention,
            'raw_point_grad': point_grad
        }
    
    def parse_smiles_structure(self, smiles):
        """
        简化的SMILES解析，提取原子和键信息
        
        Args:
            smiles: SMILES字符串
            
        Returns:
            tuple: (atoms, bonds, coordinates)
        """
        # 尝试使用RDKit进行精确解析
        try:
            from rdkit import Chem
            from rdkit.Chem import rdDepictor
            
            mol = Chem.MolFromSmiles(smiles)
            if mol is not None:
                # 生成2D坐标
                rdDepictor.Compute2DCoords(mol)
                
                # 提取原子信息
                atoms = []
                coordinates = []
                for atom in mol.GetAtoms():
                    atoms.append(atom.GetSymbol())
                    pos = mol.GetConformer().GetAtomPosition(atom.GetIdx())
                    coordinates.append([pos.x, pos.y])
                
                # 提取键信息
                bonds = []
                for bond in mol.GetBonds():
                    bonds.append((bond.GetBeginAtomIdx(), bond.GetEndAtomIdx(), bond.GetBondType()))
                
                return atoms, bonds, np.array(coordinates)
        except ImportError:
            pass
        
        # 备用简化解析方法
        return self._simple_smiles_parse(smiles)
    
    def _simple_smiles_parse(self, smiles):
        """
        简化的SMILES解析方法（备用）
        """
        # 移除括号和数字，提取原子符号
        atoms = []
        atom_pattern = r'[A-Z][a-z]?'
        matches = re.findall(atom_pattern, smiles)
        
        for match in matches:
            atoms.append(match)
        
        # 生成简单的线性坐标
        n_atoms = len(atoms)
        if n_atoms == 0:
            return [], [], np.array([])
        
        coordinates = []
        for i in range(n_atoms):
            x = i * 1.5  # 原子间距
            y = 0.5 * np.sin(i * 0.5)  # 轻微波动
            coordinates.append([x, y])
        
        # 生成简单的键连接（相邻原子）
        bonds = []
        for i in range(n_atoms - 1):
            bonds.append((i, i + 1, 1))  # 单键
        
        return atoms, bonds, np.array(coordinates)
    
    def visualize_molecular_attention(self, smiles, attention_weights, save_path):
        """
        可视化分子结构注意力
        
        Args:
            smiles: SMILES字符串
            attention_weights: 注意力权重数组
            save_path: 保存路径
        """
        print(f"正在生成分子注意力可视化: {smiles}")
        
        # 解析分子结构
        atoms, bonds, coordinates = self.parse_smiles_structure(smiles)
        
        if len(atoms) == 0:
            print(f"无法解析SMILES: {smiles}")
            return
        
        # 调整注意力权重长度
        n_atoms = len(atoms)
        if len(attention_weights) > n_atoms:
            attention_weights = attention_weights[:n_atoms]
        elif len(attention_weights) < n_atoms:
            # 扩展权重数组
            extended_weights = np.zeros(n_atoms)
            extended_weights[:len(attention_weights)] = attention_weights
            attention_weights = extended_weights
        
        # 归一化注意力权重
        if np.sum(attention_weights) > 0:
            attention_weights = attention_weights / np.max(attention_weights)
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS']
        plt.rcParams['axes.unicode_minus'] = False

        # 创建图形 - 只显示分子结构图
        fig, ax = plt.subplots(1, 1, figsize=(10, 8))
        fig.suptitle(f'分子结构注意力可视化: {smiles}', fontsize=14, fontweight='bold')
        
        # 绘制分子结构图
        self._draw_molecular_structure(ax, atoms, bonds, coordinates, attention_weights)
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
        print(f"分子注意力可视化已保存到: {save_path}")
        
        return fig
    
    def _draw_molecular_structure(self, ax, atoms, bonds, coordinates, attention_weights):
        """绘制简洁的分子结构图"""
        if len(coordinates) == 0:
            ax.text(0.5, 0.5, '无法显示分子结构', ha='center', va='center',
                   transform=ax.transAxes, fontsize=14)
            return

        # 标准化坐标
        if coordinates.shape[0] > 1:
            coordinates = coordinates - np.mean(coordinates, axis=0)
            max_range = np.max(np.abs(coordinates))
            if max_range > 0:
                coordinates = coordinates / max_range * 3
        
        # 绘制键（简化版本）
        for bond in bonds:
            if len(bond) >= 2:
                start_idx, end_idx = bond[0], bond[1]
                if start_idx < len(coordinates) and end_idx < len(coordinates):
                    start_pos = coordinates[start_idx]
                    end_pos = coordinates[end_idx]
                    # 统一使用黑色单线绘制所有键
                    ax.plot([start_pos[0], end_pos[0]], [start_pos[1], end_pos[1]],
                           'k-', linewidth=2, zorder=1)
        
        # 绘制原子（简化版本）
        for i, (atom, pos) in enumerate(zip(atoms, coordinates)):
            attention = attention_weights[i] if i < len(attention_weights) else 0

            # 原子颜色
            atom_color = self.atom_colors.get(atom, self.atom_colors['default'])

            # 固定原子大小，不根据注意力改变
            base_size = 300

            # 绘制原子
            ax.scatter(pos[0], pos[1], s=base_size, c=atom_color,
                      edgecolors='black', linewidth=1, alpha=0.8, zorder=3)

            # 高注意力原子添加简单的绿色圆圈标记
            if attention > 0.3:  # 高注意力阈值
                circle = patches.Circle(pos, radius=0.15,
                                      fill=False, edgecolor='green',
                                      linewidth=3, alpha=0.8, zorder=4)
                ax.add_patch(circle)

            # 添加原子标签（所有非氢原子都显示）
            if atom != 'H':  # 氢原子通常不显示
                # 根据原子颜色选择文本颜色
                text_color = 'white' if atom_color in ['#0000FF', '#FF0000', '#9400D3'] else 'black'
                ax.text(pos[0], pos[1], atom, ha='center', va='center',
                       fontsize=10, fontweight='bold', color=text_color, zorder=5)
        
        ax.set_aspect('equal')
        ax.set_title('分子结构图 (绿色圆圈=高注意力)', fontsize=12, fontweight='bold')
        ax.axis('off')  # 隐藏坐标轴，使图形更简洁
    

