import torch
import torch.nn.functional as F
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import re
from typing import Dict, List, Tuple, Optional
import warnings

class MolecularAttentionVisualizer:
    """
    增强版分子结构注意力可视化器
    提取和可视化分子结构的注意力权重，支持多种注意力计算方法
    """

    def __init__(self, model, device='cpu'):
        """
        初始化分子注意力可视化器

        Args:
            model: 训练好的模型
            device: 计算设备
        """
        self.model = model
        self.device = device
        self.model.to(device)
        self.model.eval()

        # 标准CPK原子颜色映射
        self.atom_colors = {
            'C': '#909090',  # 灰色 - 碳
            'N': '#3050F8',  # 蓝色 - 氮
            'O': '#FF0D0D',  # 红色 - 氧
            'S': '#FFFF30',  # 黄色 - 硫
            'P': '#FF8000',  # 橙色 - 磷
            'F': '#90E050',  # 浅绿色 - 氟
            'Cl': '#1FF01F', # 绿色 - 氯
            'Br': '#A62929', # 深红色 - 溴
            'I': '#940094',  # 紫色 - 碘
            'H': '#FFFFFF',  # 白色 - 氢
            'B': '#FFB5B5',  # 粉色 - 硼
            'Si': '#F0C8A0', # 浅棕色 - 硅
            'default': '#FF1493'  # 深粉色 - 其他
        }

        # 注册钩子来捕获中间层的激活值
        self.activations = {}
        self.gradients = {}
        self._register_hooks()

    def _register_hooks(self):
        """注册前向和反向传播钩子"""
        def save_activation(name):
            def hook(module, input, output):
                self.activations[name] = output.detach()
            return hook

        def save_gradient(name):
            def hook(module, grad_input, grad_output):
                if grad_output[0] is not None:
                    self.gradients[name] = grad_output[0].detach()
            return hook

        # 注册钩子到关键层
        if hasattr(self.model, 'smiles_encoder'):
            self.model.smiles_encoder.register_forward_hook(save_activation('smiles_features'))
            self.model.smiles_encoder.register_backward_hook(save_gradient('smiles_grad'))

        if hasattr(self.model, 'point_encoder'):
            self.model.point_encoder.register_forward_hook(save_activation('point_features'))
            self.model.point_encoder.register_backward_hook(save_gradient('point_grad'))

    def extract_attention_weights(self, smiles_tensor, point_tensor, method='gradient'):
        """
        提取注意力权重 - 增强版本

        Args:
            smiles_tensor: SMILES张量
            point_tensor: 3D点云张量
            method: 注意力计算方法 ('gradient', 'integrated_gradients', 'guided_backprop')

        Returns:
            dict: 包含注意力权重的字典
        """
        if method == 'gradient':
            return self._extract_gradient_attention(smiles_tensor, point_tensor)
        elif method == 'integrated_gradients':
            return self._extract_integrated_gradients_attention(smiles_tensor, point_tensor)
        elif method == 'guided_backprop':
            return self._extract_guided_backprop_attention(smiles_tensor, point_tensor)
        else:
            raise ValueError(f"不支持的方法: {method}")

    def _extract_gradient_attention(self, smiles_tensor, point_tensor):
        """使用标准梯度方法提取注意力权重"""
        # 移动数据到设备
        smiles_tensor = smiles_tensor.to(self.device).long()
        point_tensor = point_tensor.to(self.device).float()

        # 清空之前的激活值和梯度
        self.activations.clear()
        self.gradients.clear()

        # 前向传播
        outputs = self.model(smiles_tensor, point_tensor)
        probs = F.softmax(outputs, dim=1)

        # 计算对最大概率类别的梯度
        max_class = torch.argmax(probs, dim=1)
        target_prob = probs[0, max_class[0]]

        # 反向传播获取梯度
        self.model.zero_grad()
        target_prob.backward(retain_graph=True)

        # 从3D点云数据中提取原子级注意力
        atom_attention = self._compute_atom_attention_from_point_data(point_tensor)

        # 从SMILES数据中提取序列级注意力
        smiles_attention = self._compute_smiles_attention(smiles_tensor)

        return {
            'atom_attention': atom_attention,
            'smiles_attention': smiles_attention,
            'method': 'gradient',
            'prediction_probs': probs.detach().cpu().numpy()[0],
            'predicted_class': max_class.item()
        }

    def _compute_atom_attention_from_point_data(self, point_tensor):
        """从3D点云数据计算原子级注意力权重"""
        # point_tensor shape: [batch_size, max_atoms, max_atoms*2 + 1]
        batch_size, max_atoms = point_tensor.shape[0], point_tensor.shape[1]

        # 提取原子特征（最后一列）
        atom_features = point_tensor[:, :, -1]  # [batch_size, max_atoms]

        # 如果有梯度信息，使用梯度计算注意力
        if hasattr(point_tensor, 'grad') and point_tensor.grad is not None:
            # 使用梯度的绝对值作为注意力权重
            point_grad = point_tensor.grad.abs()[0]  # [max_atoms, features]
            atom_attention = torch.sum(point_grad, dim=1).cpu().numpy()  # [max_atoms]
        else:
            # 备用方法：基于原子特征的重要性
            atom_attention = torch.abs(atom_features[0]).cpu().numpy()

        # 只保留有效原子（非零原子）
        valid_atoms = atom_features[0].cpu().numpy() > 0
        atom_attention = atom_attention * valid_atoms

        # 归一化
        if np.sum(atom_attention) > 0:
            atom_attention = atom_attention / np.sum(atom_attention)

        return atom_attention

    def _compute_smiles_attention(self, smiles_tensor):
        """计算SMILES序列的注意力权重"""
        # 简化方法：基于非零token的均匀分布
        smiles_seq = smiles_tensor[0].cpu().numpy()
        valid_positions = smiles_seq > 0

        smiles_attention = np.zeros_like(smiles_seq, dtype=float)
        if np.sum(valid_positions) > 0:
            smiles_attention[valid_positions] = 1.0 / np.sum(valid_positions)

        return smiles_attention

    def _extract_integrated_gradients_attention(self, smiles_tensor, point_tensor, steps=20):
        """使用集成梯度方法提取注意力权重"""
        smiles_tensor = smiles_tensor.to(self.device).long()
        point_tensor = point_tensor.to(self.device).float()

        # 创建基线
        smiles_baseline = torch.zeros_like(smiles_tensor)
        point_baseline = torch.zeros_like(point_tensor)

        # 累积梯度
        integrated_grads_point = torch.zeros_like(point_tensor)

        for step in range(steps):
            # 线性插值
            alpha = step / steps
            smiles_interpolated = smiles_baseline + alpha * (smiles_tensor - smiles_baseline)
            point_interpolated = point_baseline + alpha * (point_tensor - point_baseline)
            point_interpolated.requires_grad_(True)

            # 前向传播
            outputs = self.model(smiles_interpolated, point_interpolated)
            probs = F.softmax(outputs, dim=1)
            max_class = torch.argmax(probs, dim=1)
            target_prob = probs[0, max_class[0]]

            # 反向传播
            self.model.zero_grad()
            target_prob.backward(retain_graph=True)

            # 累积梯度
            if point_interpolated.grad is not None:
                integrated_grads_point += point_interpolated.grad

        # 平均梯度
        integrated_grads_point /= steps

        # 计算注意力权重
        atom_attention = self._compute_atom_attention_from_integrated_grads(
            point_tensor, integrated_grads_point)
        smiles_attention = self._compute_smiles_attention(smiles_tensor)

        return {
            'atom_attention': atom_attention,
            'smiles_attention': smiles_attention,
            'method': 'integrated_gradients'
        }

    def _extract_guided_backprop_attention(self, smiles_tensor, point_tensor):
        """使用引导反向传播方法提取注意力权重"""
        # 简化实现：使用修改的ReLU进行引导反向传播
        smiles_tensor = smiles_tensor.to(self.device).long()
        point_tensor = point_tensor.to(self.device).float()
        point_tensor.requires_grad_(True)

        # 前向传播
        outputs = self.model(smiles_tensor, point_tensor)
        probs = F.softmax(outputs, dim=1)
        max_class = torch.argmax(probs, dim=1)
        target_prob = probs[0, max_class[0]]

        # 反向传播
        self.model.zero_grad()
        target_prob.backward()

        # 只保留正梯度（引导反向传播的简化版本）
        if point_tensor.grad is not None:
            guided_grads = torch.clamp(point_tensor.grad, min=0)
            atom_attention = self._compute_atom_attention_from_integrated_grads(
                point_tensor, guided_grads)
        else:
            atom_attention = np.zeros(point_tensor.shape[1])

        smiles_attention = self._compute_smiles_attention(smiles_tensor)

        return {
            'atom_attention': atom_attention,
            'smiles_attention': smiles_attention,
            'method': 'guided_backprop'
        }

    def _compute_atom_attention_from_integrated_grads(self, point_tensor, gradients):
        """从集成梯度计算原子注意力"""
        # 对特征维度求和得到每个原子的注意力
        atom_attention = torch.sum(torch.abs(gradients[0]), dim=1).cpu().numpy()

        # 只保留有效原子
        atom_features = point_tensor[0, :, -1].cpu().numpy()
        valid_atoms = atom_features > 0
        atom_attention = atom_attention * valid_atoms

        # 归一化
        if np.sum(atom_attention) > 0:
            atom_attention = atom_attention / np.sum(atom_attention)

        return atom_attention

    def parse_smiles_structure(self, smiles):
        """
        简化的SMILES解析，提取原子和键信息
        
        Args:
            smiles: SMILES字符串
            
        Returns:
            tuple: (atoms, bonds, coordinates)
        """
        # 尝试使用RDKit进行精确解析
        try:
            from rdkit import Chem
            from rdkit.Chem import rdDepictor
            
            mol = Chem.MolFromSmiles(smiles)
            if mol is not None:
                # 生成2D坐标
                rdDepictor.Compute2DCoords(mol)
                
                # 提取原子信息
                atoms = []
                coordinates = []
                for atom in mol.GetAtoms():
                    atoms.append(atom.GetSymbol())
                    pos = mol.GetConformer().GetAtomPosition(atom.GetIdx())
                    coordinates.append([pos.x, pos.y])
                
                # 提取键信息
                bonds = []
                for bond in mol.GetBonds():
                    bonds.append((bond.GetBeginAtomIdx(), bond.GetEndAtomIdx(), bond.GetBondType()))
                
                return atoms, bonds, np.array(coordinates)
        except ImportError:
            pass
        
        # 备用简化解析方法
        return self._simple_smiles_parse(smiles)
    
    def _simple_smiles_parse(self, smiles):
        """
        简化的SMILES解析方法（备用）
        """
        # 移除括号和数字，提取原子符号
        atoms = []
        atom_pattern = r'[A-Z][a-z]?'
        matches = re.findall(atom_pattern, smiles)
        
        for match in matches:
            atoms.append(match)
        
        # 生成简单的线性坐标
        n_atoms = len(atoms)
        if n_atoms == 0:
            return [], [], np.array([])
        
        coordinates = []
        for i in range(n_atoms):
            x = i * 1.5  # 原子间距
            y = 0.5 * np.sin(i * 0.5)  # 轻微波动
            coordinates.append([x, y])
        
        # 生成简单的键连接（相邻原子）
        bonds = []
        for i in range(n_atoms - 1):
            bonds.append((i, i + 1, 1))  # 单键
        
        return atoms, bonds, np.array(coordinates)
    
    def visualize_molecular_attention(self, smiles, attention_data, save_path,
                                    visualization_type='comprehensive'):
        """
        可视化分子结构注意力 - 增强版本

        Args:
            smiles: SMILES字符串
            attention_data: 注意力数据字典
            save_path: 保存路径
            visualization_type: 可视化类型 ('simple', 'comprehensive', 'comparison')
        """
        print(f"正在生成分子注意力可视化: {smiles}")

        # 解析分子结构
        atoms, bonds, coordinates = self.parse_smiles_structure(smiles)

        if len(atoms) == 0:
            print(f"无法解析SMILES: {smiles}")
            return

        # 提取注意力权重
        if isinstance(attention_data, dict) and 'atom_attention' in attention_data:
            attention_weights = attention_data['atom_attention']
        else:
            attention_weights = attention_data  # 向后兼容

        # 调整注意力权重长度
        attention_weights = self._adjust_attention_weights(attention_weights, len(atoms))

        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS']
        plt.rcParams['axes.unicode_minus'] = False

        if visualization_type == 'simple':
            return self._create_simple_visualization(smiles, atoms, bonds, coordinates,
                                                   attention_weights, save_path)
        elif visualization_type == 'comprehensive':
            return self._create_comprehensive_visualization(smiles, atoms, bonds, coordinates,
                                                          attention_weights, attention_data, save_path)
        elif visualization_type == 'comparison':
            return self._create_comparison_visualization(smiles, atoms, bonds, coordinates,
                                                       attention_data, save_path)
        else:
            raise ValueError(f"不支持的可视化类型: {visualization_type}")

    def _adjust_attention_weights(self, attention_weights, n_atoms):
        """调整注意力权重数组的长度"""
        if len(attention_weights) > n_atoms:
            attention_weights = attention_weights[:n_atoms]
        elif len(attention_weights) < n_atoms:
            extended_weights = np.zeros(n_atoms)
            extended_weights[:len(attention_weights)] = attention_weights
            attention_weights = extended_weights

        # 归一化注意力权重
        if np.sum(attention_weights) > 0:
            attention_weights = attention_weights / np.max(attention_weights)

        return attention_weights

    def _create_simple_visualization(self, smiles, atoms, bonds, coordinates,
                                   attention_weights, save_path):
        """创建简单的分子注意力可视化"""
        fig, ax = plt.subplots(1, 1, figsize=(10, 8))
        fig.suptitle(f'分子结构注意力可视化: {smiles}', fontsize=14, fontweight='bold')

        self._draw_molecular_structure(ax, atoms, bonds, coordinates, attention_weights)

        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
        print(f"分子注意力可视化已保存到: {save_path}")

        return fig

    def _create_comprehensive_visualization(self, smiles, atoms, bonds, coordinates,
                                          attention_weights, attention_data, save_path):
        """创建综合的分子注意力可视化"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle(f'分子结构注意力综合分析: {smiles}', fontsize=16, fontweight='bold')

        # 1. 分子结构图
        self._draw_molecular_structure(ax1, atoms, bonds, coordinates, attention_weights)
        ax1.set_title('分子结构注意力图', fontsize=12, fontweight='bold')

        # 2. 注意力权重柱状图
        self._draw_attention_bar_chart(ax2, atoms, attention_weights)

        # 3. 注意力热力图
        self._draw_attention_heatmap(ax3, atoms, attention_weights)

        # 4. 分析信息
        self._draw_analysis_info(ax4, attention_data, atoms, attention_weights)

        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
        print(f"综合分子注意力可视化已保存到: {save_path}")

        return fig

    def _create_comparison_visualization(self, smiles, atoms, bonds, coordinates,
                                       attention_data, save_path):
        """创建多方法比较的可视化"""
        if not isinstance(attention_data, dict) or len(attention_data) < 2:
            print("需要多个方法的注意力数据进行比较")
            return None

        methods = list(attention_data.keys())
        n_methods = len(methods)

        fig, axes = plt.subplots(2, n_methods, figsize=(6*n_methods, 12))
        if n_methods == 1:
            axes = axes.reshape(2, 1)

        fig.suptitle(f'多方法分子注意力对比: {smiles}', fontsize=16, fontweight='bold')

        for i, method in enumerate(methods):
            method_data = attention_data[method]
            if isinstance(method_data, dict) and 'atom_attention' in method_data:
                attention_weights = method_data['atom_attention']
            else:
                attention_weights = method_data

            attention_weights = self._adjust_attention_weights(attention_weights, len(atoms))

            # 上排：分子结构图
            self._draw_molecular_structure(axes[0, i], atoms, bonds, coordinates, attention_weights)
            axes[0, i].set_title(f'{method} - 分子结构', fontsize=12, fontweight='bold')

            # 下排：注意力柱状图
            self._draw_attention_bar_chart(axes[1, i], atoms, attention_weights)
            axes[1, i].set_title(f'{method} - 注意力分布', fontsize=12, fontweight='bold')

        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
        print(f"多方法对比可视化已保存到: {save_path}")

        return fig

    def _draw_attention_bar_chart(self, ax, atoms, attention_weights):
        """绘制注意力权重柱状图"""
        # 只显示有效原子
        valid_indices = [i for i, atom in enumerate(atoms) if atom != 'H']
        valid_atoms = [atoms[i] for i in valid_indices]
        valid_weights = [attention_weights[i] for i in valid_indices]

        if len(valid_atoms) == 0:
            ax.text(0.5, 0.5, '无有效原子', ha='center', va='center', transform=ax.transAxes)
            return

        # 创建颜色映射
        colors = [self.atom_colors.get(atom, self.atom_colors['default']) for atom in valid_atoms]

        bars = ax.bar(range(len(valid_atoms)), valid_weights, color=colors, alpha=0.7,
                     edgecolor='black', linewidth=1)

        # 添加数值标签
        for i, (bar, weight) in enumerate(zip(bars, valid_weights)):
            if weight > 0.01:  # 只显示较大的权重
                ax.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 0.01,
                       f'{weight:.3f}', ha='center', va='bottom', fontsize=8)

        ax.set_xticks(range(len(valid_atoms)))
        ax.set_xticklabels([f'{atom}{i}' for i, atom in enumerate(valid_atoms)], rotation=45)
        ax.set_ylabel('注意力权重', fontsize=10)
        ax.grid(True, alpha=0.3, axis='y')

    def _draw_attention_heatmap(self, ax, atoms, attention_weights):
        """绘制注意力热力图"""
        # 创建简单的热力图矩阵
        n_atoms = len(atoms)
        heatmap_data = np.zeros((1, n_atoms))
        heatmap_data[0, :] = attention_weights

        im = ax.imshow(heatmap_data, cmap='Reds', aspect='auto', vmin=0, vmax=1)

        # 设置标签
        ax.set_xticks(range(n_atoms))
        ax.set_xticklabels([f'{atom}{i}' for i, atom in enumerate(atoms)], rotation=45)
        ax.set_yticks([0])
        ax.set_yticklabels(['注意力'])
        ax.set_title('注意力热力图', fontsize=12, fontweight='bold')

        # 添加颜色条
        plt.colorbar(im, ax=ax, fraction=0.046, pad=0.04)

    def _draw_analysis_info(self, ax, attention_data, atoms, attention_weights):
        """绘制分析信息"""
        ax.axis('off')

        # 找出最重要的原子
        top_indices = np.argsort(attention_weights)[-3:][::-1]  # 前3个最重要的原子
        top_atoms = [(i, atoms[i], attention_weights[i]) for i in top_indices if attention_weights[i] > 0.01]

        # 计算统计信息
        method = attention_data.get('method', '未知') if isinstance(attention_data, dict) else '未知'
        max_attention = np.max(attention_weights)
        mean_attention = np.mean(attention_weights[attention_weights > 0])

        info_text = f"""
分子注意力分析结果

🔬 分析方法: {method}
📊 原子总数: {len(atoms)}
📈 最大注意力: {max_attention:.4f}
📊 平均注意力: {mean_attention:.4f}

🎯 重要原子排序:
"""

        for i, (idx, atom, weight) in enumerate(top_atoms[:3]):
            info_text += f"  {i+1}. {atom}{idx}: {weight:.4f}\n"

        if isinstance(attention_data, dict) and 'prediction_probs' in attention_data:
            probs = attention_data['prediction_probs']
            predicted_class = attention_data.get('predicted_class', 0)
            info_text += f"\n🎯 预测结果:\n"
            info_text += f"  预测类别: {predicted_class}\n"
            info_text += f"  预测概率: {probs[predicted_class]:.4f}\n"

        ax.text(0.05, 0.95, info_text, transform=ax.transAxes,
                fontsize=10, verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle="round,pad=0.5", facecolor="lightblue", alpha=0.8))

    def _draw_molecular_structure(self, ax, atoms, bonds, coordinates, attention_weights):
        """绘制简洁的分子结构图"""
        if len(coordinates) == 0:
            ax.text(0.5, 0.5, '无法显示分子结构', ha='center', va='center',
                   transform=ax.transAxes, fontsize=14)
            return

        # 标准化坐标
        if coordinates.shape[0] > 1:
            coordinates = coordinates - np.mean(coordinates, axis=0)
            max_range = np.max(np.abs(coordinates))
            if max_range > 0:
                coordinates = coordinates / max_range * 3
        
        # 绘制键（简化版本）
        for bond in bonds:
            if len(bond) >= 2:
                start_idx, end_idx = bond[0], bond[1]
                if start_idx < len(coordinates) and end_idx < len(coordinates):
                    start_pos = coordinates[start_idx]
                    end_pos = coordinates[end_idx]
                    # 统一使用黑色单线绘制所有键
                    ax.plot([start_pos[0], end_pos[0]], [start_pos[1], end_pos[1]],
                           'k-', linewidth=2, zorder=1)
        
        # 绘制原子（简化版本）
        for i, (atom, pos) in enumerate(zip(atoms, coordinates)):
            attention = attention_weights[i] if i < len(attention_weights) else 0

            # 原子颜色
            atom_color = self.atom_colors.get(atom, self.atom_colors['default'])

            # 固定原子大小，不根据注意力改变
            base_size = 300

            # 绘制原子
            ax.scatter(pos[0], pos[1], s=base_size, c=atom_color,
                      edgecolors='black', linewidth=1, alpha=0.8, zorder=3)

            # 高注意力原子添加简单的绿色圆圈标记
            if attention > 0.3:  # 高注意力阈值
                circle = patches.Circle(pos, radius=0.15,
                                      fill=False, edgecolor='green',
                                      linewidth=3, alpha=0.8, zorder=4)
                ax.add_patch(circle)

            # 添加原子标签（所有非氢原子都显示）
            if atom != 'H':  # 氢原子通常不显示
                # 根据原子颜色选择文本颜色
                text_color = 'white' if atom_color in ['#0000FF', '#FF0000', '#9400D3'] else 'black'
                ax.text(pos[0], pos[1], atom, ha='center', va='center',
                       fontsize=10, fontweight='bold', color=text_color, zorder=5)
        
        ax.set_aspect('equal')
        ax.set_title('分子结构图 (绿色圆圈=高注意力)', fontsize=12, fontweight='bold')
        ax.axis('off')  # 隐藏坐标轴，使图形更简洁
    

