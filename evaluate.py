import numpy as np
import pandas as pd
import torch
from sklearn.metrics import roc_curve
import matplotlib.pyplot as plt
from model import FishLC50Predictor
from utils import prepare_data, evaluate_model

def evaluate_model_performance(model_path='models/fish_lc50_predictor.pth', 
                             data_path='input_data.xlsx'):
    """
    评估模型性能
    
    Args:
        model_path: 模型文件路径
        data_path: 数据文件路径
    """
    # 读取数据
    df = pd.read_excel(data_path)
    smiles_data, graph_data, labels = prepare_data(df)
    
    # 加载模型
    model = FishLC50Predictor()
    model.load_state_dict(torch.load(model_path, map_location='cpu'))
    model.eval()
    
    if torch.cuda.is_available():
        model = model.cuda()
        smiles_data = smiles_data.cuda()
        graph_data = graph_data.cuda()
    
    # 预测
    predictions = []
    true_labels = labels.numpy()
    
    with torch.no_grad():
        batch_size = 32
        for i in range(0, len(smiles_data), batch_size):
            batch_smiles = smiles_data[i:i+batch_size]
            batch_graph = graph_data[i:i+batch_size]
            
            outputs = model(batch_smiles, batch_graph)
            probs = torch.softmax(outputs, dim=1)[:, 1]
            predictions.extend(probs.cpu().numpy())
    
    # 评估性能
    auc_score, aupr_score, balanced_accuracy, sensitivity, specificity = evaluate_model(predictions, true_labels)
    
    print("=== 模型性能评估结果 ===")
    print(f"AUC: {auc_score:.4f}")
    print(f"AUPR: {aupr_score:.4f}")
    print(f"平衡准确率: {balanced_accuracy:.4f}")
    print(f"敏感性: {sensitivity:.4f}")
    print(f"特异性: {specificity:.4f}")
    
    # 绘制ROC曲线
    fpr, tpr, _ = roc_curve(true_labels, predictions)
    plt.figure(figsize=(8, 6))
    plt.plot(fpr, tpr, label=f'ROC Curve (AUC = {auc_score:.4f})')
    plt.plot([0, 1], [0, 1], 'k--', label='Random')
    plt.xlabel('False Positive Rate')
    plt.ylabel('True Positive Rate')
    plt.title('ROC Curve for FishLC50 Prediction')
    plt.legend()
    plt.grid(True)
    plt.savefig('roc_curve.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    return auc_score, aupr_score, balanced_accuracy, sensitivity, specificity


if __name__ == '__main__':
    try:
        evaluate_model_performance()
    except FileNotFoundError:
        print("错误: 未找到模型文件或数据文件。请检查文件路径。")
    except Exception as e:
        print(f"评估过程中出现错误: {e}")