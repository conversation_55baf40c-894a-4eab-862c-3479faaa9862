import torch
import torch.nn.functional as F
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Rectangle

class ChannelAnalyzer:
    """
    通道重要性分析器
    使用梯度归因法计算SMILES和3D结构通道的相对贡献
    """
    
    def __init__(self, model, device='cpu'):
        """
        初始化通道分析器
        
        Args:
            model: 训练好的模型
            device: 计算设备
        """
        self.model = model
        self.device = device
        self.model.to(device)
        self.model.eval()
    
    def compute_channel_importance(self, data_loader):
        """
        计算通道重要性

        Args:
            data_loader: 数据加载器

        Returns:
            dict: 包含各通道重要性分数的字典
        """
        print("正在计算通道重要性...")

        smiles_gradients = []
        point_gradients = []

        for batch_idx, (smiles_batch, point_batch, labels_batch) in enumerate(data_loader):
            # 移动数据到设备
            smiles_batch = smiles_batch.to(self.device).long()  # SMILES需要long类型用于embedding
            point_batch = point_batch.to(self.device).float()
            labels_batch = labels_batch.to(self.device)

            # 只对point_batch启用梯度计算（因为它是float类型）
            point_batch.requires_grad_(True)

            # 对于SMILES，我们使用敏感性分析方法

            # 前向传播并注册hook
            self.model.eval()
            outputs = self.model(smiles_batch, point_batch)

            # 获取SMILES embedding的输出并注册hook
            with torch.enable_grad():
                smiles_batch_for_grad = smiles_batch.clone().detach()
                point_batch_for_grad = point_batch.clone().detach().requires_grad_(True)

                # 重新前向传播以获取可计算梯度的输出
                outputs = self.model(smiles_batch_for_grad, point_batch_for_grad)

                # 计算损失
                loss = F.cross_entropy(outputs, labels_batch)

                # 反向传播
                loss.backward()

                # 收集point_batch的梯度
                if point_batch_for_grad.grad is not None:
                    point_grad = point_batch_for_grad.grad.abs().sum().item()
                    point_gradients.append(point_grad)

                # 对于SMILES，我们使用一个近似方法：
                # 计算输出对SMILES embedding的敏感性
                smiles_sensitivity = self._compute_smiles_sensitivity(smiles_batch_for_grad, point_batch_for_grad, labels_batch)
                smiles_gradients.append(smiles_sensitivity)

            # 清除梯度
            self.model.zero_grad()

            print(f"处理批次 {batch_idx + 1}/{len(data_loader)}")

        # 计算平均重要性
        smiles_importance = np.mean(smiles_gradients) if smiles_gradients else 0.0
        point_importance = np.mean(point_gradients) if point_gradients else 0.0

        results = {
            'smiles_importance': smiles_importance,
            'point_importance': point_importance,
            'smiles_gradients': smiles_gradients,
            'point_gradients': point_gradients
        }

        print(f"SMILES通道重要性: {smiles_importance:.4f}")
        print(f"3D结构通道重要性: {point_importance:.4f}")

        return results

    def _compute_smiles_sensitivity(self, smiles_batch, point_batch, labels_batch):
        """
        计算SMILES通道的敏感性（近似梯度重要性）

        Args:
            smiles_batch: SMILES批次数据
            point_batch: 点云批次数据
            labels_batch: 标签批次数据

        Returns:
            float: SMILES通道敏感性分数
        """
        # 获取原始输出
        with torch.no_grad():
            original_outputs = self.model(smiles_batch, point_batch)
            original_probs = F.softmax(original_outputs, dim=1)

        # 通过扰动SMILES输入来估计敏感性
        sensitivity_scores = []

        # 对每个样本进行小幅扰动
        for i in range(smiles_batch.size(0)):
            sample_smiles = smiles_batch[i:i+1].clone()
            sample_point = point_batch[i:i+1].clone()

            # 创建扰动版本（随机改变一些token）
            perturbed_smiles = sample_smiles.clone()
            num_perturbations = min(3, sample_smiles.size(1) // 10)  # 扰动10%的token

            for _ in range(num_perturbations):
                pos = torch.randint(0, sample_smiles.size(1), (1,)).item()
                # 随机改变token（在合理范围内）
                perturbed_smiles[0, pos] = torch.randint(1, 65, (1,)).item()

            # 计算扰动后的输出
            with torch.no_grad():
                perturbed_outputs = self.model(perturbed_smiles, sample_point)
                perturbed_probs = F.softmax(perturbed_outputs, dim=1)

            # 计算概率变化
            prob_change = torch.abs(original_probs[i:i+1] - perturbed_probs).sum().item()
            sensitivity_scores.append(prob_change)

        return np.mean(sensitivity_scores) * 1000  # 放大以匹配梯度量级
    
    def visualize_channel_importance(self, importance_results, save_path='channel_importance.png'):
        """
        可视化通道重要性对比
        
        Args:
            importance_results: 重要性计算结果
            save_path: 保存路径
        """
        print("正在生成通道重要性可视化...")
        
        # 设置中文字体和样式
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS']
        plt.rcParams['axes.unicode_minus'] = False
        plt.style.use('default')  # 使用默认样式确保一致性
        
        # 创建图形
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('双通道模型重要性分析 (梯度归因法)', fontsize=18, fontweight='bold', y=0.95)
        
        # 提取数据
        smiles_imp = importance_results['smiles_importance']
        point_imp = importance_results['point_importance']
        
        # 配色方案 - 深蓝色和深紫红色
        colors = ['#2E86AB', '#A23B72']  # 深蓝色, 深紫红色
        channel_names = ['SMILES通道', '3D结构通道']
        importance_values = [smiles_imp, point_imp]
        
        # 1. 重要性分数柱状图
        bars = ax1.bar(channel_names, importance_values, color=colors, alpha=0.8, 
                      edgecolor='white', linewidth=2)
        
        # 添加渐变效果
        for bar, color in zip(bars, colors):
            # 添加阴影效果
            shadow = Rectangle((bar.get_x()-0.02, bar.get_y()-0.02), 
                             bar.get_width(), bar.get_height(),
                             facecolor='gray', alpha=0.3, zorder=0)
            ax1.add_patch(shadow)
            
            # 在柱子上添加数值标签
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + max(importance_values)*0.01,
                    f'{height:.3f}', ha='center', va='bottom', fontweight='bold', fontsize=12)
        
        ax1.set_title('通道重要性分数对比', fontsize=14, fontweight='bold', pad=20)
        ax1.set_ylabel('梯度重要性分数', fontsize=12)
        ax1.grid(True, alpha=0.3, axis='y')
        ax1.set_ylim(0, max(importance_values) * 1.15)
        
        # 2. 贡献比例饼图
        total_importance = smiles_imp + point_imp
        if total_importance > 0:
            proportions = [smiles_imp/total_importance, point_imp/total_importance]

            _, texts, autotexts = ax2.pie(proportions, labels=channel_names, colors=colors,
                                         autopct='%1.1f%%', startangle=90,
                                         explode=(0.05, 0.05), shadow=True)
            
            # 美化饼图文本
            for autotext in autotexts:
                autotext.set_color('white')
                autotext.set_fontweight('bold')
                autotext.set_fontsize(12)
            
            for text in texts:
                text.set_fontsize(11)
                text.set_fontweight('bold')
        else:
            ax2.text(0.5, 0.5, '无有效数据', ha='center', va='center', 
                    transform=ax2.transAxes, fontsize=14)
        
        ax2.set_title('通道贡献比例', fontsize=14, fontweight='bold', pad=20)
        
        # 3. 梯度分布箱线图
        gradient_data = [importance_results['smiles_gradients'], 
                        importance_results['point_gradients']]
        
        box_plot = ax3.boxplot(gradient_data, labels=channel_names, patch_artist=True,
                              boxprops=dict(facecolor='lightblue', alpha=0.7),
                              medianprops=dict(color='red', linewidth=2))
        
        # 为箱线图着色
        for patch, color in zip(box_plot['boxes'], colors):
            patch.set_facecolor(color)
            patch.set_alpha(0.7)
        
        ax3.set_title('梯度分布分析', fontsize=14, fontweight='bold', pad=20)
        ax3.set_ylabel('梯度幅值', fontsize=12)
        ax3.grid(True, alpha=0.3, axis='y')
        
        # 4. 详细说明文本
        ax4.axis('off')
        explanation_text = f"""
梯度归因法通道重要性分析结果

📊 重要性分数:
• SMILES通道: {smiles_imp:.4f}
• 3D结构通道: {point_imp:.4f}

📈 计算方法:
• 使用梯度归因法 (Gradient Attribution)
• 公式: 重要性 = Σ|∂Loss/∂Features|
• 数值越大表示该通道对预测影响越大

🔍 结果解读:
• 高分数(>1000): 显著影响
• 中等分数(100-1000): 一定影响  
• 低分数(<100): 影响较小

⚙️ 技术细节:
• 批次数: {len(importance_results['smiles_gradients'])}
• 损失函数: 交叉熵损失
• 梯度计算: 反向传播
        """
        
        ax4.text(0.05, 0.95, explanation_text, transform=ax4.transAxes, 
                fontsize=11, verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle="round,pad=0.5", facecolor="lightgray", alpha=0.8))
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
        print(f"通道重要性可视化已保存到: {save_path}")
        
        return fig
