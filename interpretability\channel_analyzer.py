import torch
import torch.nn.functional as F
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Rectangle
import copy

class ChannelAnalyzer:
    """
    增强版通道重要性分析器
    使用多种梯度归因方法计算SMILES和3D结构通道的相对贡献
    """

    def __init__(self, model, device='cpu'):
        """
        初始化通道分析器

        Args:
            model: 训练好的模型
            device: 计算设备
        """
        self.model = model
        self.device = device
        self.model.to(device)
        self.model.eval()

        # 存储中间激活值的钩子
        self.activations = {}
        self.gradients = {}
        self._register_hooks()

    def _register_hooks(self):
        """注册前向和反向传播钩子来捕获中间激活值和梯度"""
        def save_activation(name):
            def hook(module, input, output):
                self.activations[name] = output.detach()
            return hook

        def save_gradient(name):
            def hook(module, grad_input, grad_output):
                self.gradients[name] = grad_output[0].detach()
            return hook

        # 注册钩子到SMILES和3D结构编码器的输出
        self.model.smiles_encoder.register_forward_hook(save_activation('smiles_features'))
        self.model.point_encoder.register_forward_hook(save_activation('point_features'))
        self.model.smiles_encoder.register_backward_hook(save_gradient('smiles_grad'))
        self.model.point_encoder.register_backward_hook(save_gradient('point_grad'))

    def compute_channel_importance(self, data_loader, method='gradient_attribution'):
        """
        计算通道重要性 - 增强版本

        Args:
            data_loader: 数据加载器
            method: 计算方法 ('gradient_attribution', 'integrated_gradients', 'feature_ablation')

        Returns:
            dict: 包含各通道重要性分数的字典
        """
        print(f"正在使用{method}方法计算通道重要性...")

        if method == 'gradient_attribution':
            return self._compute_gradient_attribution(data_loader)
        elif method == 'integrated_gradients':
            return self._compute_integrated_gradients(data_loader)
        elif method == 'feature_ablation':
            return self._compute_feature_ablation(data_loader)
        else:
            raise ValueError(f"不支持的方法: {method}")

    def _compute_gradient_attribution(self, data_loader):
        """使用梯度归因法计算通道重要性"""
        smiles_importances = []
        point_importances = []

        for batch_idx, (smiles_batch, point_batch, labels_batch) in enumerate(data_loader):
            # 移动数据到设备
            smiles_batch = smiles_batch.to(self.device).long()
            point_batch = point_batch.to(self.device).float()
            labels_batch = labels_batch.to(self.device)

            # 清空之前的激活值和梯度
            self.activations.clear()
            self.gradients.clear()

            # 前向传播
            self.model.eval()
            outputs = self.model(smiles_batch, point_batch)

            # 计算损失
            loss = F.cross_entropy(outputs, labels_batch)

            # 反向传播
            self.model.zero_grad()
            loss.backward(retain_graph=True)

            # 从钩子中获取特征和梯度
            if 'smiles_features' in self.activations and 'smiles_grad' in self.gradients:
                smiles_features = self.activations['smiles_features']
                smiles_grad = self.gradients['smiles_grad']
                # 计算SMILES通道的重要性：特征 × 梯度
                smiles_importance = torch.abs(smiles_features * smiles_grad).sum().item()
                smiles_importances.append(smiles_importance)

            if 'point_features' in self.activations and 'point_grad' in self.gradients:
                point_features = self.activations['point_features']
                point_grad = self.gradients['point_grad']
                # 计算3D结构通道的重要性：特征 × 梯度
                point_importance = torch.abs(point_features * point_grad).sum().item()
                point_importances.append(point_importance)

            print(f"处理批次 {batch_idx + 1}/{len(data_loader)}")

        # 计算平均重要性
        smiles_importance = np.mean(smiles_importances) if smiles_importances else 0.0
        point_importance = np.mean(point_importances) if point_importances else 0.0

        results = {
            'smiles_importance': smiles_importance,
            'point_importance': point_importance,
            'smiles_gradients': smiles_importances,
            'point_gradients': point_importances,
            'method': 'gradient_attribution'
        }

        print(f"SMILES通道重要性: {smiles_importance:.4f}")
        print(f"3D结构通道重要性: {point_importance:.4f}")

        return results

    def _compute_integrated_gradients(self, data_loader, steps=50):
        """使用集成梯度法计算通道重要性"""
        smiles_importances = []
        point_importances = []

        for batch_idx, (smiles_batch, point_batch, labels_batch) in enumerate(data_loader):
            smiles_batch = smiles_batch.to(self.device).long()
            point_batch = point_batch.to(self.device).float()
            labels_batch = labels_batch.to(self.device)

            # 创建基线（零输入）
            smiles_baseline = torch.zeros_like(smiles_batch)
            point_baseline = torch.zeros_like(point_batch)

            # 计算集成梯度
            smiles_integrated_grad = 0
            point_integrated_grad = 0

            for step in range(steps):
                # 线性插值
                alpha = step / steps
                smiles_interpolated = smiles_baseline + alpha * (smiles_batch - smiles_baseline)
                point_interpolated = point_baseline + alpha * (point_batch - point_baseline)

                # 清空激活值和梯度
                self.activations.clear()
                self.gradients.clear()

                # 前向传播
                outputs = self.model(smiles_interpolated, point_interpolated)
                loss = F.cross_entropy(outputs, labels_batch)

                # 反向传播
                self.model.zero_grad()
                loss.backward(retain_graph=True)

                # 累积梯度
                if 'smiles_grad' in self.gradients:
                    smiles_integrated_grad += self.gradients['smiles_grad']
                if 'point_grad' in self.gradients:
                    point_integrated_grad += self.gradients['point_grad']

            # 计算最终的集成梯度
            smiles_integrated_grad /= steps
            point_integrated_grad /= steps

            # 计算重要性分数
            if 'smiles_features' in self.activations:
                smiles_importance = torch.abs(self.activations['smiles_features'] * smiles_integrated_grad).sum().item()
                smiles_importances.append(smiles_importance)

            if 'point_features' in self.activations:
                point_importance = torch.abs(self.activations['point_features'] * point_integrated_grad).sum().item()
                point_importances.append(point_importance)

            print(f"处理批次 {batch_idx + 1}/{len(data_loader)} (集成梯度)")

        results = {
            'smiles_importance': np.mean(smiles_importances) if smiles_importances else 0.0,
            'point_importance': np.mean(point_importances) if point_importances else 0.0,
            'smiles_gradients': smiles_importances,
            'point_gradients': point_importances,
            'method': 'integrated_gradients'
        }

        return results

    def _compute_feature_ablation(self, data_loader):
        """使用特征消融法计算通道重要性"""
        smiles_importances = []
        point_importances = []

        for batch_idx, (smiles_batch, point_batch, labels_batch) in enumerate(data_loader):
            smiles_batch = smiles_batch.to(self.device).long()
            point_batch = point_batch.to(self.device).float()
            labels_batch = labels_batch.to(self.device)

            # 获取原始预测
            with torch.no_grad():
                original_outputs = self.model(smiles_batch, point_batch)
                original_probs = F.softmax(original_outputs, dim=1)

            # 消融SMILES通道（使用零输入）
            smiles_zero = torch.zeros_like(smiles_batch)
            with torch.no_grad():
                ablated_smiles_outputs = self.model(smiles_zero, point_batch)
                ablated_smiles_probs = F.softmax(ablated_smiles_outputs, dim=1)

            # 消融3D结构通道（使用零输入）
            point_zero = torch.zeros_like(point_batch)
            with torch.no_grad():
                ablated_point_outputs = self.model(smiles_batch, point_zero)
                ablated_point_probs = F.softmax(ablated_point_outputs, dim=1)

            # 计算重要性（预测概率的变化）
            smiles_importance = torch.abs(original_probs - ablated_smiles_probs).sum().item()
            point_importance = torch.abs(original_probs - ablated_point_probs).sum().item()

            smiles_importances.append(smiles_importance)
            point_importances.append(point_importance)

            print(f"处理批次 {batch_idx + 1}/{len(data_loader)} (特征消融)")

        results = {
            'smiles_importance': np.mean(smiles_importances) if smiles_importances else 0.0,
            'point_importance': np.mean(point_importances) if point_importances else 0.0,
            'smiles_gradients': smiles_importances,
            'point_gradients': point_importances,
            'method': 'feature_ablation'
        }

        return results

    def compare_methods(self, data_loader):
        """
        比较不同方法的通道重要性分析结果

        Args:
            data_loader: 数据加载器

        Returns:
            dict: 包含所有方法结果的字典
        """
        print("正在比较不同的通道重要性分析方法...")

        methods = ['gradient_attribution', 'integrated_gradients', 'feature_ablation']
        results = {}

        for method in methods:
            print(f"\n使用{method}方法...")
            results[method] = self.compute_channel_importance(data_loader, method=method)

        return results
    
    def visualize_channel_importance(self, importance_results, save_path='channel_importance.png'):
        """
        可视化通道重要性对比 - 增强版本

        Args:
            importance_results: 重要性计算结果（可以是单个方法或多个方法的结果）
            save_path: 保存路径
        """
        print("正在生成通道重要性可视化...")

        # 设置中文字体和样式
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS']
        plt.rcParams['axes.unicode_minus'] = False
        plt.style.use('default')

        # 检查是否是多方法比较结果
        if isinstance(importance_results, dict) and 'gradient_attribution' in importance_results:
            return self._visualize_method_comparison(importance_results, save_path)
        else:
            return self._visualize_single_method(importance_results, save_path)

    def _visualize_single_method(self, importance_results, save_path):
        """可视化单个方法的结果"""
        # 创建图形
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        method_name = importance_results.get('method', '梯度归因法')
        fig.suptitle(f'双通道模型重要性分析 ({method_name})', fontsize=18, fontweight='bold', y=0.95)
        
        # 提取数据
        smiles_imp = importance_results['smiles_importance']
        point_imp = importance_results['point_importance']
        
        # 配色方案 - 深蓝色和深紫红色
        colors = ['#2E86AB', '#A23B72']  # 深蓝色, 深紫红色
        channel_names = ['SMILES通道', '3D结构通道']
        importance_values = [smiles_imp, point_imp]
        
        # 1. 重要性分数柱状图
        bars = ax1.bar(channel_names, importance_values, color=colors, alpha=0.8, 
                      edgecolor='white', linewidth=2)
        
        # 添加渐变效果
        for bar, color in zip(bars, colors):
            # 添加阴影效果
            shadow = Rectangle((bar.get_x()-0.02, bar.get_y()-0.02), 
                             bar.get_width(), bar.get_height(),
                             facecolor='gray', alpha=0.3, zorder=0)
            ax1.add_patch(shadow)
            
            # 在柱子上添加数值标签
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + max(importance_values)*0.01,
                    f'{height:.3f}', ha='center', va='bottom', fontweight='bold', fontsize=12)
        
        ax1.set_title('通道重要性分数对比', fontsize=14, fontweight='bold', pad=20)
        ax1.set_ylabel('梯度重要性分数', fontsize=12)
        ax1.grid(True, alpha=0.3, axis='y')
        ax1.set_ylim(0, max(importance_values) * 1.15)
        
        # 2. 贡献比例饼图
        total_importance = smiles_imp + point_imp
        if total_importance > 0:
            proportions = [smiles_imp/total_importance, point_imp/total_importance]

            _, texts, autotexts = ax2.pie(proportions, labels=channel_names, colors=colors,
                                         autopct='%1.1f%%', startangle=90,
                                         explode=(0.05, 0.05), shadow=True)
            
            # 美化饼图文本
            for autotext in autotexts:
                autotext.set_color('white')
                autotext.set_fontweight('bold')
                autotext.set_fontsize(12)
            
            for text in texts:
                text.set_fontsize(11)
                text.set_fontweight('bold')
        else:
            ax2.text(0.5, 0.5, '无有效数据', ha='center', va='center', 
                    transform=ax2.transAxes, fontsize=14)
        
        ax2.set_title('通道贡献比例', fontsize=14, fontweight='bold', pad=20)
        
        # 3. 梯度分布箱线图
        gradient_data = [importance_results['smiles_gradients'], 
                        importance_results['point_gradients']]
        
        box_plot = ax3.boxplot(gradient_data, labels=channel_names, patch_artist=True,
                              boxprops=dict(facecolor='lightblue', alpha=0.7),
                              medianprops=dict(color='red', linewidth=2))
        
        # 为箱线图着色
        for patch, color in zip(box_plot['boxes'], colors):
            patch.set_facecolor(color)
            patch.set_alpha(0.7)
        
        ax3.set_title('梯度分布分析', fontsize=14, fontweight='bold', pad=20)
        ax3.set_ylabel('梯度幅值', fontsize=12)
        ax3.grid(True, alpha=0.3, axis='y')
        
        # 4. 详细说明文本
        ax4.axis('off')
        explanation_text = f"""
梯度归因法通道重要性分析结果

📊 重要性分数:
• SMILES通道: {smiles_imp:.4f}
• 3D结构通道: {point_imp:.4f}

📈 计算方法:
• 使用梯度归因法 (Gradient Attribution)
• 公式: 重要性 = Σ|∂Loss/∂Features|
• 数值越大表示该通道对预测影响越大

🔍 结果解读:
• 高分数(>1000): 显著影响
• 中等分数(100-1000): 一定影响  
• 低分数(<100): 影响较小

⚙️ 技术细节:
• 批次数: {len(importance_results['smiles_gradients'])}
• 损失函数: 交叉熵损失
• 梯度计算: 反向传播
        """
        
        ax4.text(0.05, 0.95, explanation_text, transform=ax4.transAxes, 
                fontsize=11, verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle="round,pad=0.5", facecolor="lightgray", alpha=0.8))
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
        print(f"通道重要性可视化已保存到: {save_path}")

        return fig

    def _visualize_method_comparison(self, all_results, save_path):
        """可视化多种方法的比较结果"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(18, 14))
        fig.suptitle('多方法通道重要性分析对比', fontsize=18, fontweight='bold', y=0.95)

        methods = list(all_results.keys())
        method_names = {
            'gradient_attribution': '梯度归因法',
            'integrated_gradients': '集成梯度法',
            'feature_ablation': '特征消融法'
        }

        # 提取数据
        smiles_scores = [all_results[method]['smiles_importance'] for method in methods]
        point_scores = [all_results[method]['point_importance'] for method in methods]
        method_labels = [method_names.get(method, method) for method in methods]

        # 1. SMILES通道重要性对比
        colors_smiles = ['#2E86AB', '#4A90E2', '#6BB6FF']
        bars1 = ax1.bar(method_labels, smiles_scores, color=colors_smiles[:len(methods)],
                       alpha=0.8, edgecolor='white', linewidth=2)

        for bar in bars1:
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + max(smiles_scores)*0.01,
                    f'{height:.3f}', ha='center', va='bottom', fontweight='bold', fontsize=10)

        ax1.set_title('SMILES通道重要性对比', fontsize=14, fontweight='bold')
        ax1.set_ylabel('重要性分数', fontsize=12)
        ax1.grid(True, alpha=0.3, axis='y')
        ax1.tick_params(axis='x', rotation=45)

        # 2. 3D结构通道重要性对比
        colors_point = ['#A23B72', '#C44569', '#E55A4E']
        bars2 = ax2.bar(method_labels, point_scores, color=colors_point[:len(methods)],
                       alpha=0.8, edgecolor='white', linewidth=2)

        for bar in bars2:
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + max(point_scores)*0.01,
                    f'{height:.3f}', ha='center', va='bottom', fontweight='bold', fontsize=10)

        ax2.set_title('3D结构通道重要性对比', fontsize=14, fontweight='bold')
        ax2.set_ylabel('重要性分数', fontsize=12)
        ax2.grid(True, alpha=0.3, axis='y')
        ax2.tick_params(axis='x', rotation=45)

        # 3. 相对贡献比例对比
        x = np.arange(len(methods))
        width = 0.35

        total_scores = [s + p for s, p in zip(smiles_scores, point_scores)]
        smiles_ratios = [s/t*100 if t > 0 else 0 for s, t in zip(smiles_scores, total_scores)]
        point_ratios = [p/t*100 if t > 0 else 0 for p, t in zip(point_scores, total_scores)]

        bars3 = ax3.bar(x - width/2, smiles_ratios, width, label='SMILES通道',
                       color='#2E86AB', alpha=0.8)
        bars4 = ax3.bar(x + width/2, point_ratios, width, label='3D结构通道',
                       color='#A23B72', alpha=0.8)

        ax3.set_title('通道贡献比例对比 (%)', fontsize=14, fontweight='bold')
        ax3.set_ylabel('贡献比例 (%)', fontsize=12)
        ax3.set_xticks(x)
        ax3.set_xticklabels(method_labels, rotation=45)
        ax3.legend()
        ax3.grid(True, alpha=0.3, axis='y')

        # 4. 方法说明和结果总结
        ax4.axis('off')
        summary_text = f"""
多方法通道重要性分析总结

📊 分析方法:
• 梯度归因法: 特征×梯度的绝对值
• 集成梯度法: 从基线到输入的路径积分
• 特征消融法: 移除特征后的预测变化

📈 SMILES通道重要性:
• 梯度归因法: {smiles_scores[0]:.4f}
• 集成梯度法: {smiles_scores[1]:.4f} (如果可用)
• 特征消融法: {smiles_scores[2]:.4f} (如果可用)

📈 3D结构通道重要性:
• 梯度归因法: {point_scores[0]:.4f}
• 集成梯度法: {point_scores[1]:.4f} (如果可用)
• 特征消融法: {point_scores[2]:.4f} (如果可用)

🔍 一致性分析:
• 方法间的结果一致性可以验证分析的可靠性
• 不同方法可能突出不同方面的重要性
        """

        ax4.text(0.05, 0.95, summary_text, transform=ax4.transAxes,
                fontsize=10, verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle="round,pad=0.5", facecolor="lightgray", alpha=0.8))

        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
        print(f"多方法通道重要性对比可视化已保存到: {save_path}")

        return fig
