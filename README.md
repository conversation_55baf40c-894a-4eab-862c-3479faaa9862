# Model Interpretability Analysis

## Overview

This module provides comprehensive interpretability analysis for the dual-channel molecular toxicity prediction model, focusing on channel importance and molecular-level explanations.

## Features

### Phase 1: Channel Analysis
- **Channel Importance Comparison**: Quantifies the relative contribution of SMILES and 3D structure channels
- **Ablation Study Visualization**: Enhanced visualization of ablation experiment results
- **Performance Impact Analysis**: Measures the performance drop when removing the 3D structure channel

### Phase 2: Molecular Explanation
- **SMILES Sequence Heatmap**: Identifies important characters/substructures in SMILES strings
- **3D Structure Hotspots**: Locates critical atoms and regions in 3D molecular structures
- **Functional Group Analysis**: Analyzes the toxicity contribution of different functional groups

## Usage

### Prerequisites
Ensure you have trained models available:
- `models/fish_lc50_predictor.pth` (full model)
- `models/fish_lc50_predictor_smiles_only.pth` (ablation model)

### Running Analysis
```bash
python interpretability_main.py
```

### Output Files
Results are saved in the `results/` directory:
- `channel_importance.png` - Channel importance comparison
- `ablation_analysis.png` - Ablation study results
- `smiles_heatmap_sample.png` - SMILES sequence importance heatmap
- `3d_hotspots_sample.png` - 3D structure hotspot analysis
- `functional_groups.png` - Functional group importance ranking
- `interpretability_report.txt` - Comprehensive analysis report

## Technical Details

### Channel Importance Calculation
Uses gradient-based attribution to compute the importance of each channel:
- Calculates gradients with respect to input features
- Applies gradient × input method for importance scoring
- Normalizes scores for comparison

### Molecular Explanation Methods
- **Gradient Attribution**: Computes feature importance using backpropagation
- **Integrated Gradients**: Provides stable importance scores for molecular features
- **Functional Group Recognition**: Uses SMARTS patterns to identify chemical substructures

### Supported Functional Groups
- Benzene rings
- Hydroxyl groups
- Carboxyl groups
- Amino groups
- Nitro groups
- Carbonyl groups
- Ether linkages
- Halogens
- Sulfur-containing groups
- Phosphorus-containing groups

## Requirements

- PyTorch
- RDKit
- NumPy
- Matplotlib
- Seaborn
- Pandas
- Scikit-learn