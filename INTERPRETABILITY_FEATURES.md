# 双通道模型可解释性分析功能

## 🎯 功能概述

本项目实现了两个核心的可解释性分析功能：

1. **通道重要性对比可视化** - 量化SMILES和3D结构通道的相对贡献
2. **分子结构注意力可视化** - 识别模型关注的关键分子结构部分

## 📊 1. 通道重要性对比可视化

### 🔬 技术原理
- **方法**: 梯度归因法 (Gradient Attribution)
- **公式**: 重要性 = Σ|∂Loss/∂Features|
- **目标**: 量化SMILES和3D结构通道对模型预测的相对贡献

### 🎨 可视化特点
- **配色方案**: 深蓝色(#2E86AB) + 深紫红色(#A23B72)
- **布局**: 2×2子图布局
  - 左上: 重要性分数柱状图（带渐变和阴影效果）
  - 右上: 贡献比例饼图（带爆炸效果和阴影）
  - 左下: 梯度分布箱线图
  - 右下: 详细的技术说明和结果解读

### 📈 输出内容
- 各通道的重要性分数
- 相对贡献百分比
- 梯度分布统计
- 计算方法说明
- 结果解读指南

## 🧪 2. 分子结构注意力可视化

### 🔍 技术原理
- **方法**: 基于模型梯度的注意力权重计算
- **提取**: 通过反向传播获取特征重要性
- **映射**: 将注意力权重映射到分子原子

### 🌈 可视化特点
- **原子颜色**: 标准CPK颜色方案
  - 碳(C): 灰色 #909090
  - 氮(N): 蓝色 #3050F8
  - 氧(O): 红色 #FF0D0D
  - 硫(S): 黄色 #FFFF30
  - 其他原子有相应标准颜色

- **注意力表示**: 橙色渐变系统
  - 原子大小: 基础大小 + 注意力权重 × 放大系数
  - 边框颜色: 高注意力原子显示橙色边框
  - 光晕效果: 超高注意力原子显示橙色光晕

### 🧬 分子结构特点
- **键线显示**: 黑色键线连接原子
- **双键支持**: 平行线表示双键
- **智能布局**: 自动坐标标准化和缩放
- **原子标签**: 非碳原子显示元素符号

### 📊 权重分析
- **柱状图**: 显示各原子的注意力权重分布
- **重要性排名**: 列出前3个最重要的原子
- **颜色编码**: 橙色深浅表示重要性程度
- **高亮标记**: 高注意力原子特殊边框标记

## 🚀 使用方法

### 基本使用
```python
# 运行完整分析
python interpretability.py

# 运行测试
python test_interpretability.py

# 运行演示
python demo_interpretability.py
```

### 自定义使用
```python
from interpretability.channel_analyzer import ChannelAnalyzer
from interpretability.molecular_attention_visualizer import MolecularAttentionVisualizer

# 创建分析器
channel_analyzer = ChannelAnalyzer(model, device)
attention_visualizer = MolecularAttentionVisualizer(model, device)

# 分析通道重要性
results = channel_analyzer.compute_channel_importance(data_loader)
channel_analyzer.visualize_channel_importance(results, save_path)

# 分析分子注意力
attention_data = attention_visualizer.extract_attention_weights(smiles_tensor, point_tensor)
attention_visualizer.visualize_molecular_attention(smiles, attention_data['attention_weights'], save_path)
```

## 📁 输出文件

### 通道重要性分析
- `results/channel_importance.png` - 通道重要性对比图
- 包含重要性分数、贡献比例、分布分析和技术说明

### 分子结构注意力可视化
- `results/molecular_attention_sample_*.png` - 分子注意力图
- 每个图包含分子结构图和权重柱状图

### 分析报告
- `results/interpretability_report.txt` - 详细的分析报告
- 包含数值结果、方法说明和结果解读

## 🔧 技术实现细节

### 数据类型处理
- **SMILES数据**: Long类型用于embedding层
- **3D点云数据**: Float类型用于梯度计算
- **自动转换**: 智能处理不同数据类型的梯度计算

### 梯度计算策略
- **3D结构**: 直接梯度计算
- **SMILES**: 敏感性分析方法（扰动测试）
- **归一化**: 自动权重归一化和缩放

### 可视化优化
- **中文字体**: 支持中文显示
- **高DPI**: 300 DPI高清输出
- **专业配色**: 科学可视化标准配色
- **响应式布局**: 自适应图形尺寸

## 🎨 设计理念

### 科学性
- 基于成熟的梯度归因理论
- 使用标准的化学可视化惯例
- 提供详细的方法说明和结果解读

### 美观性
- 专业的配色方案
- 清晰的图形布局
- 丰富的视觉效果

### 实用性
- 直观的结果展示
- 详细的数值分析
- 便于理解的说明文档

## 🔍 应用场景

### 模型分析
- 理解双通道模型的工作机制
- 评估不同数据源的贡献
- 优化模型架构设计

### 科学研究
- 识别与毒性相关的分子结构
- 分析化学基团的重要性
- 支持药物设计和毒理学研究

### 结果解释
- 向非技术人员解释模型决策
- 提供可信的预测依据
- 支持监管和审查需求

## ✅ 质量保证

### 代码质量
- 完整的错误处理
- 详细的文档注释
- 模块化设计架构

### 功能测试
- 单元测试覆盖
- 集成测试验证
- 边界情况处理

### 用户体验
- 清晰的使用说明
- 丰富的示例代码
- 完善的错误提示
