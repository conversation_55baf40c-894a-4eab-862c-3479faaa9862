import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import torch
from sklearn.metrics import roc_curve, auc, precision_recall_curve, confusion_matrix
from model import FishLC50Predictor
from utils import evaluate_model
from analysis_functions_en import create_detailed_analysis, print_performance_summary

def load_trained_model(model_path='models/fish_lc50_predictor.pth', dropout_rate=0.4):
    """加载训练好的模型"""
    model = FishLC50Predictor(dropout_rate=dropout_rate)
    model.load_state_dict(torch.load(model_path, map_location='cpu'))
    model.eval()
    return model

def get_predictions(model, data_loader):
    """获取模型预测结果"""
    predictions = []
    true_labels = []
    
    with torch.no_grad():
        for smiles_batch, graph_batch, labels_batch in data_loader:
            if torch.cuda.is_available():
                smiles_batch = smiles_batch.cuda()
                graph_batch = graph_batch.cuda()
                model = model.cuda()
            
            outputs = model(smiles_batch, graph_batch)
            probs = torch.softmax(outputs, dim=1)[:, 1]  # 获取正类概率
            
            predictions.extend(probs.cpu().numpy())
            true_labels.extend(labels_batch.numpy())
    
    return np.array(predictions), np.array(true_labels)

def create_scatter_plot(train_preds, train_true, test_preds, test_true, save_path='prediction_scatter.png'):
    """创建预测值vs真实值的散点图"""
    
    # 创建图形
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('Prediction vs True Value Visualization Analysis', fontsize=16, fontweight='bold')
    
    # 颜色设置
    colors = ['#FF6B6B', '#4ECDC4']  # 红色表示负类，青色表示正类
    alpha = 0.6
    
    # 1. 训练集散点图
    ax1 = axes[0, 0]
    for label in [0, 1]:
        mask = train_true == label
        ax1.scatter(train_true[mask], train_preds[mask], 
                   c=colors[label], alpha=alpha, s=50,
                   label=f'Class {label} (n={np.sum(mask)})')
    
    ax1.plot([0, 1], [0, 1], 'k--', alpha=0.5, linewidth=2, label='Perfect Prediction')
    ax1.axhline(y=0.5, color='gray', linestyle=':', alpha=0.7, label='Classification Threshold (0.5)')
    ax1.set_xlabel('True Labels')
    ax1.set_ylabel('Predicted Probability')
    ax1.set_title('Training Set Predictions')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    ax1.set_xlim(-0.1, 1.1)
    ax1.set_ylim(-0.05, 1.05)
    
    # 2. 测试集散点图
    ax2 = axes[0, 1]
    for label in [0, 1]:
        mask = test_true == label
        ax2.scatter(test_true[mask], test_preds[mask], 
                   c=colors[label], alpha=alpha, s=50,
                   label=f'Class {label} (n={np.sum(mask)})')
    
    ax2.plot([0, 1], [0, 1], 'k--', alpha=0.5, linewidth=2, label='Perfect Prediction')
    ax2.axhline(y=0.5, color='gray', linestyle=':', alpha=0.7, label='Classification Threshold (0.5)')
    ax2.set_xlabel('True Labels')
    ax2.set_ylabel('Predicted Probability')
    ax2.set_title('Test Set Predictions')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    ax2.set_xlim(-0.1, 1.1)
    ax2.set_ylim(-0.05, 1.05)
    
    # 3. ROC曲线对比
    ax3 = axes[1, 0]
    
    # 训练集ROC
    train_fpr, train_tpr, _ = roc_curve(train_true, train_preds)
    train_auc = auc(train_fpr, train_tpr)
    ax3.plot(train_fpr, train_tpr, color='blue', linewidth=2, 
             label=f'Training Set (AUC = {train_auc:.3f})')
    
    # 测试集ROC
    test_fpr, test_tpr, _ = roc_curve(test_true, test_preds)
    test_auc = auc(test_fpr, test_tpr)
    ax3.plot(test_fpr, test_tpr, color='red', linewidth=2, 
             label=f'Test Set (AUC = {test_auc:.3f})')
    
    ax3.plot([0, 1], [0, 1], 'k--', alpha=0.5, linewidth=1, label='Random Classifier')
    ax3.set_xlabel('False Positive Rate (FPR)')
    ax3.set_ylabel('True Positive Rate (TPR)')
    ax3.set_title('ROC Curve Comparison')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 4. 预测概率分布
    ax4 = axes[1, 1]
    
    # 训练集分布
    ax4.hist(train_preds[train_true == 0], bins=30, alpha=0.5, color=colors[0], 
             label=f'Train-Negative (n={np.sum(train_true == 0)})', density=True)
    ax4.hist(train_preds[train_true == 1], bins=30, alpha=0.5, color=colors[1], 
             label=f'Train-Positive (n={np.sum(train_true == 1)})', density=True)
    
    # 测试集分布（用边框表示）
    ax4.hist(test_preds[test_true == 0], bins=30, alpha=0.3, color=colors[0], 
             histtype='step', linewidth=2, linestyle='--',
             label=f'Test-Negative (n={np.sum(test_true == 0)})', density=True)
    ax4.hist(test_preds[test_true == 1], bins=30, alpha=0.3, color=colors[1], 
             histtype='step', linewidth=2, linestyle='--',
             label=f'Test-Positive (n={np.sum(test_true == 1)})', density=True)
    
    ax4.axvline(x=0.5, color='gray', linestyle=':', alpha=0.7, label='Classification Threshold')
    ax4.set_xlabel('Predicted Probability')
    ax4.set_ylabel('Density')
    ax4.set_title('Predicted Probability Distribution')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.show()
    
    return fig